<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="container">
    <h1 class="mb-4"><i class="fas fa-tachometer-alt me-2"></i>Tableau de bord des archives</h1>

    <!-- Statistiques générales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stat-card bg-primary text-white">
                <div class="card-body text-center">
                    <div class="stat-icon">
                        <i class="fas fa-archive"></i>
                    </div>
                    <div class="stat-value"><?= $stats['total'] ?></div>
                    <div class="stat-label">Total des archives</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card bg-success text-white">
                <div class="card-body text-center">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-value"><?= $stats['active'] ?></div>
                    <div class="stat-label">Archives actives</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card bg-secondary text-white">
                <div class="card-body text-center">
                    <div class="stat-icon">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="stat-value"><?= $stats['inactive'] ?></div>
                    <div class="stat-label">Archives inactives</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card bg-warning">
                <div class="card-body text-center">
                    <div class="stat-icon">
                        <i class="fas fa-box-archive"></i>
                    </div>
                    <div class="stat-value"><?= $stats['archived'] ?></div>
                    <div class="stat-label">Archives archivées</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Statistiques par catégorie -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-folder me-2"></i>Archives par catégorie
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (count($stats['by_category']) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Catégorie</th>
                                    <th>Nombre</th>
                                    <th>Pourcentage</th>
                                    <th>Graphique</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($stats['by_category'] as $category): ?>
                                <tr>
                                    <td><?= htmlspecialchars($category['name']) ?></td>
                                    <td><?= $category['count'] ?></td>
                                    <td><?= round(($category['count'] / $stats['total']) * 100, 1) ?>%</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-primary" role="progressbar"
                                                style="width: <?= ($category['count'] / $stats['total']) * 100 ?>%"
                                                aria-valuenow="<?= $category['count'] ?>" aria-valuemin="0"
                                                aria-valuemax="<?= $stats['total'] ?>"></div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Aucune donnée disponible.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Statistiques par mois -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar me-2"></i>Archives par mois (<?= date('Y') ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (count($stats['by_month']) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Mois</th>
                                    <th>Nombre</th>
                                    <th>Graphique</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                    $months = [
                                        1 => 'Janvier', 2 => 'Février', 3 => 'Mars', 4 => 'Avril',
                                        5 => 'Mai', 6 => 'Juin', 7 => 'Juillet', 8 => 'Août',
                                        9 => 'Septembre', 10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
                                    ];
                                    
                                    $max_count = 0;
                                    foreach ($stats['by_month'] as $month) {
                                        if ($month['count'] > $max_count) {
                                            $max_count = $month['count'];
                                        }
                                    }
                                    
                                    foreach ($stats['by_month'] as $month): ?>
                                <tr>
                                    <td><?= $months[$month['month']] ?></td>
                                    <td><?= $month['count'] ?></td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                style="width: <?= ($month['count'] / $max_count) * 100 ?>%"
                                                aria-valuenow="<?= $month['count'] ?>" aria-valuemin="0"
                                                aria-valuemax="<?= $max_count ?>"></div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Aucune donnée disponible.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Archives récentes -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-clock me-2"></i>Archives récentes
            </h5>
        </div>
        <div class="card-body p-0">
            <?php if (count($recent_archives) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Titre</th>
                            <th>Catégorie</th>
                            <th>Date</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_archives as $archive): ?>
                        <tr>
                            <td><?= htmlspecialchars($archive['reference_number']) ?></td>
                            <td><?= htmlspecialchars($archive['title']) ?></td>
                            <td><?= htmlspecialchars($archive['category_name']) ?></td>
                            <td><?= formatDate($archive['created_at']) ?></td>
                            <td>
                                <?php if ($archive['status'] == 'active'): ?>
                                <span class="badge bg-success">Actif</span>
                                <?php elseif ($archive['status'] == 'inactive'): ?>
                                <span class="badge bg-secondary">Inactif</span>
                                <?php else: ?>
                                <span class="badge bg-warning text-dark">Archivé</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?= BASE_URL ?>archives/view/<?= $archive['id'] ?>"
                                        class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="Voir les détails">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= BASE_URL ?>archives/download/<?= $archive['id'] ?>"
                                        class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Télécharger">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="alert alert-info m-3">
                <i class="fas fa-info-circle me-2"></i>Aucune archive récente.
            </div>
            <?php endif; ?>
        </div>
        <div class="card-footer text-center">
            <a href="<?= BASE_URL ?>archives" class="btn btn-primary">
                <i class="fas fa-list me-2"></i>Voir toutes les archives
            </a>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>