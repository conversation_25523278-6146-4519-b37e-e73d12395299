<?php
require_once __DIR__ . '/../config/database.php';

/**
 * Modèle pour la gestion des archives
 */
class ArchiveModel
{
    private $db;
    private $table = 'archives';

    /**
     * Constructeur
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Récupère toutes les archives
     * @param array $options Options de filtrage et de pagination
     * @return array Les archives
     */
    public function getAll($options = [])
    {
        // Construire la requête SQL de base
        $sql = "SELECT a.*, c.name as category_name, u.name as created_by_name 
                FROM {$this->table} a 
                LEFT JOIN categories c ON a.category_id = c.id 
                LEFT JOIN users u ON a.created_by = u.id";

        // Tableau pour les conditions WHERE
        $conditions = [];
        $params = [];

        // Filtrage par catégorie
        if (isset($options['category_id']) && !empty($options['category_id'])) {
            $conditions[] = "a.category_id = :category_id";
            $params['category_id'] = $options['category_id'];
        }

        // Filtrage par statut
        if (isset($options['status']) && !empty($options['status'])) {
            $conditions[] = "a.status = :status";
            $params['status'] = $options['status'];
        }

        // Filtrage par date de création (début)
        if (isset($options['date_from']) && !empty($options['date_from'])) {
            $conditions[] = "a.created_at >= :date_from";
            $params['date_from'] = $options['date_from'] . ' 00:00:00';
        }

        // Filtrage par date de création (fin)
        if (isset($options['date_to']) && !empty($options['date_to'])) {
            $conditions[] = "a.created_at <= :date_to";
            $params['date_to'] = $options['date_to'] . ' 23:59:59';
        }

        // Filtrage par recherche textuelle
        if (isset($options['search']) && !empty($options['search'])) {
            $conditions[] = "(a.title LIKE :search OR a.reference_number LIKE :search OR a.description LIKE :search)";
            $params['search'] = '%' . $options['search'] . '%';
        }

        // Ajouter les conditions à la requête
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        // Tri
        $sql .= " ORDER BY " . ($options['order_by'] ?? 'a.created_at') . " " . ($options['order_dir'] ?? 'DESC');

        // Pagination
        if (isset($options['limit']) && isset($options['offset'])) {
            $sql .= " LIMIT :offset, :limit";
            $params['offset'] = $options['offset'];
            $params['limit'] = $options['limit'];
        }

        // Exécuter la requête
        return $this->db->all($sql, $params);
    }

    /**
     * Compte le nombre total d'archives
     * @param array $options Options de filtrage
     * @return int Le nombre d'archives
     */
    public function count($options = [])
    {
        // Construire la requête SQL de base
        $sql = "SELECT COUNT(*) as count FROM {$this->table} a";

        // Tableau pour les conditions WHERE
        $conditions = [];
        $params = [];

        // Filtrage par catégorie
        if (isset($options['category_id']) && !empty($options['category_id'])) {
            $conditions[] = "a.category_id = :category_id";
            $params['category_id'] = $options['category_id'];
        }

        // Filtrage par statut
        if (isset($options['status']) && !empty($options['status'])) {
            $conditions[] = "a.status = :status";
            $params['status'] = $options['status'];
        }

        // Filtrage par date de création (début)
        if (isset($options['date_from']) && !empty($options['date_from'])) {
            $conditions[] = "a.created_at >= :date_from";
            $params['date_from'] = $options['date_from'] . ' 00:00:00';
        }

        // Filtrage par date de création (fin)
        if (isset($options['date_to']) && !empty($options['date_to'])) {
            $conditions[] = "a.created_at <= :date_to";
            $params['date_to'] = $options['date_to'] . ' 23:59:59';
        }

        // Filtrage par recherche textuelle
        if (isset($options['search']) && !empty($options['search'])) {
            $conditions[] = "(a.title LIKE :search OR a.reference_number LIKE :search OR a.description LIKE :search)";
            $params['search'] = '%' . $options['search'] . '%';
        }

        // Ajouter les conditions à la requête
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        // Exécuter la requête
        $result = $this->db->single($sql, $params);

        return $result['count'];
    }

    /**
     * Récupère une archive par son ID
     * @param int $id L'ID de l'archive
     * @return array|false L'archive ou false si non trouvée
     */
    public function findById($id)
    {
        $sql = "SELECT a.*, c.name as category_name, u.name as created_by_name 
                FROM {$this->table} a 
                LEFT JOIN categories c ON a.category_id = c.id 
                LEFT JOIN users u ON a.created_by = u.id 
                WHERE a.id = :id";

        return $this->db->single($sql, ['id' => $id]);
    }

    /**
     * Récupère une archive par son numéro de référence
     * @param string $referenceNumber Le numéro de référence
     * @return array|false L'archive ou false si non trouvée
     */
    public function findByReferenceNumber($referenceNumber)
    {
        $sql = "SELECT a.*, c.name as category_name, u.name as created_by_name 
                FROM {$this->table} a 
                LEFT JOIN categories c ON a.category_id = c.id 
                LEFT JOIN users u ON a.created_by = u.id 
                WHERE a.reference_number = :reference_number";

        return $this->db->single($sql, ['reference_number' => $referenceNumber]);
    }

    /**
     * Vérifie si un numéro de référence existe déjà
     * @param string $referenceNumber Le numéro de référence à vérifier
     * @param int $excludeId ID de l'archive à exclure (pour les mises à jour)
     * @return bool True si le numéro de référence existe, false sinon
     */
    public function referenceNumberExists($referenceNumber, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE reference_number = :reference_number";
        $params = ['reference_number' => $referenceNumber];

        if ($excludeId !== null) {
            $sql .= " AND id != :id";
            $params['id'] = $excludeId;
        }

        $result = $this->db->single($sql, $params);

        return $result['count'] > 0;
    }

    /**
     * Crée une nouvelle archive
     * @param array $data Les données de l'archive
     * @return int|false L'ID de l'archive créée ou false en cas d'erreur
     */
    public function create($data)
    {
        // Démarrer une transaction
        $this->db->beginTransaction();

        try {
            // Insérer l'archive
            $sql = "INSERT INTO {$this->table} (title, reference_number, category_id, description, 
                    status, location, file_path, created_by, created_at) 
                    VALUES (:title, :reference_number, :category_id, :description, 
                    :status, :location, :file_path, :created_by, NOW())";

            $params = [
                'title' => $data['title'],
                'reference_number' => $data['reference_number'],
                'category_id' => $data['category_id'],
                'description' => $data['description'],
                'status' => $data['status'] ?? 'active',
                'location' => $data['location'] ?? null,
                'file_path' => $data['file_path'] ?? null,
                'created_by' => $data['created_by']
            ];

            $this->db->query($sql, $params);
            $archiveId = $this->db->lastInsertId();

            // Insérer les métadonnées si présentes
            if (isset($data['metadata']) && is_array($data['metadata']) && !empty($data['metadata'])) {
                foreach ($data['metadata'] as $key => $value) {
                    $sql = "INSERT INTO archive_metadata (archive_id, meta_key, meta_value, created_at) 
                            VALUES (:archive_id, :meta_key, :meta_value, NOW())";

                    $metaParams = [
                        'archive_id' => $archiveId,
                        'meta_key' => $key,
                        'meta_value' => $value
                    ];

                    $this->db->query($sql, $metaParams);
                }
            }

            // Valider la transaction
            $this->db->commit();

            return $archiveId;
        } catch (Exception $e) {
            // Annuler la transaction en cas d'erreur
            $this->db->rollback();
            return false;
        }
    }

    /**
     * Met à jour une archive
     * @param int $id L'ID de l'archive
     * @param array $data Les données à mettre à jour
     * @return bool True si la mise à jour a réussi, false sinon
     */
    public function update($id, $data)
    {
        // Démarrer une transaction
        $this->db->beginTransaction();

        try {
            // Construire la requête SQL
            $sql = "UPDATE {$this->table} SET ";
            $updates = [];
            $params = ['id' => $id];

            // Ajouter les champs à mettre à jour
            if (isset($data['title'])) {
                $updates[] = "title = :title";
                $params['title'] = $data['title'];
            }

            if (isset($data['reference_number'])) {
                $updates[] = "reference_number = :reference_number";
                $params['reference_number'] = $data['reference_number'];
            }

            if (isset($data['category_id'])) {
                $updates[] = "category_id = :category_id";
                $params['category_id'] = $data['category_id'];
            }

            if (isset($data['description'])) {
                $updates[] = "description = :description";
                $params['description'] = $data['description'];
            }

            if (isset($data['status'])) {
                $updates[] = "status = :status";
                $params['status'] = $data['status'];
            }

            if (isset($data['location'])) {
                $updates[] = "location = :location";
                $params['location'] = $data['location'];
            }

            if (isset($data['file_path'])) {
                $updates[] = "file_path = :file_path";
                $params['file_path'] = $data['file_path'];
            }

            $updates[] = "updated_at = NOW()";

            $sql .= implode(", ", $updates);
            $sql .= " WHERE id = :id";

            // Mettre à jour l'archive
            $this->db->query($sql, $params);

            // Mettre à jour les métadonnées si présentes
            if (isset($data['metadata']) && is_array($data['metadata']) && !empty($data['metadata'])) {
                foreach ($data['metadata'] as $key => $value) {
                    // Vérifier si la métadonnée existe déjà
                    $checkSql = "SELECT id FROM archive_metadata WHERE archive_id = :archive_id AND meta_key = :meta_key";
                    $checkParams = [
                        'archive_id' => $id,
                        'meta_key' => $key
                    ];

                    $existingMeta = $this->db->single($checkSql, $checkParams);

                    if ($existingMeta) {
                        // Mettre à jour la métadonnée existante
                        $metaSql = "UPDATE archive_metadata SET meta_value = :meta_value, updated_at = NOW() 
                                    WHERE archive_id = :archive_id AND meta_key = :meta_key";
                    } else {
                        // Insérer une nouvelle métadonnée
                        $metaSql = "INSERT INTO archive_metadata (archive_id, meta_key, meta_value, created_at) 
                                    VALUES (:archive_id, :meta_key, :meta_value, NOW())";
                    }

                    $metaParams = [
                        'archive_id' => $id,
                        'meta_key' => $key,
                        'meta_value' => $value
                    ];

                    $this->db->query($metaSql, $metaParams);
                }
            }

            // Valider la transaction
            $this->db->commit();

            return true;
        } catch (Exception $e) {
            // Annuler la transaction en cas d'erreur
            $this->db->rollback();
            return false;
        }
    }

    /**
     * Supprime une archive
     * @param int $id L'ID de l'archive
     * @return bool True si la suppression a réussi, false sinon
     */
    public function delete($id)
    {
        // Démarrer une transaction
        $this->db->beginTransaction();

        try {
            // Supprimer les métadonnées associées
            $metaSql = "DELETE FROM archive_metadata WHERE archive_id = :id";
            $this->db->query($metaSql, ['id' => $id]);

            // Supprimer l'archive
            $sql = "DELETE FROM {$this->table} WHERE id = :id";
            $this->db->query($sql, ['id' => $id]);

            // Valider la transaction
            $this->db->commit();

            return true;
        } catch (Exception $e) {
            // Annuler la transaction en cas d'erreur
            $this->db->rollback();
            return false;
        }
    }

    /**
     * Récupère les métadonnées d'une archive
     * @param int $archiveId L'ID de l'archive
     * @return array Les métadonnées
     */
    public function getMetadata($archiveId)
    {
        $sql = "SELECT meta_key, meta_value FROM archive_metadata WHERE archive_id = :archive_id";
        $rows = $this->db->all($sql, ['archive_id' => $archiveId]);

        $metadata = [];
        foreach ($rows as $row) {
            $metadata[$row['meta_key']] = $row['meta_value'];
        }

        return $metadata;
    }

    /**
     * Récupère les statistiques des archives
     * @return array Les statistiques
     */
    public function getStats()
    {
        $stats = [];

        // Nombre total d'archives
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        $result = $this->db->single($sql);
        $stats['total'] = $result['count'];

        // Nombre d'archives par statut
        $sql = "SELECT status, COUNT(*) as count FROM {$this->table} GROUP BY status";
        $rows = $this->db->all($sql);

        $stats['by_status'] = [];
        foreach ($rows as $row) {
            $stats['by_status'][$row['status']] = $row['count'];
        }

        // Nombre d'archives par catégorie
        $sql = "SELECT c.name, COUNT(*) as count 
                FROM {$this->table} a 
                JOIN categories c ON a.category_id = c.id 
                GROUP BY a.category_id";
        $rows = $this->db->all($sql);

        $stats['by_category'] = [];
        foreach ($rows as $row) {
            $stats['by_category'][$row['name']] = $row['count'];
        }

        // Nombre d'archives par mois (12 derniers mois)
        $sql = "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count 
                FROM {$this->table} 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH) 
                GROUP BY month 
                ORDER BY month";
        $rows = $this->db->all($sql);

        $stats['by_month'] = [];
        foreach ($rows as $row) {
            $stats['by_month'][$row['month']] = $row['count'];
        }

        return $stats;
    }

    /**
     * Génère un numéro de référence unique
     * @param string $prefix Préfixe du numéro de référence (par défaut: ARC)
     * @return string Le numéro de référence généré
     */
    public function generateReferenceNumber($prefix = 'ARC')
    {
        // Obtenir l'année courante
        $year = date('Y');

        // Obtenir le dernier numéro de séquence pour cette année
        $sql = "SELECT MAX(SUBSTRING_INDEX(reference_number, '-', -1)) as last_sequence 
                FROM {$this->table} 
                WHERE reference_number LIKE :pattern";

        $pattern = $prefix . '-' . $year . '-%';
        $result = $this->db->single($sql, ['pattern' => $pattern]);

        // Incrémenter le numéro de séquence
        $sequence = 1;
        if ($result && $result['last_sequence']) {
            $sequence = intval($result['last_sequence']) + 1;
        }

        // Formater le numéro de séquence avec des zéros à gauche
        $formattedSequence = str_pad($sequence, 5, '0', STR_PAD_LEFT);

        // Construire le numéro de référence
        $referenceNumber = $prefix . '-' . $year . '-' . $formattedSequence;

        return $referenceNumber;
    }

    /**
     * Compte les archives par statut
     * @param array $options Options de filtrage
     * @return array Nombre d'archives par statut
     */
    public function countByStatus($options = [])
    {
        $sql = "SELECT status, COUNT(*) as count FROM {$this->table} a";

        // Tableau pour les conditions WHERE
        $conditions = [];
        $params = [];

        // Filtrage par catégorie
        if (isset($options['category_id']) && !empty($options['category_id'])) {
            $conditions[] = "a.category_id = :category_id";
            $params['category_id'] = $options['category_id'];
        }

        // Filtrage par date de création (début)
        if (isset($options['date_from']) && !empty($options['date_from'])) {
            $conditions[] = "a.created_at >= :date_from";
            $params['date_from'] = $options['date_from'] . ' 00:00:00';
        }

        // Filtrage par date de création (fin)
        if (isset($options['date_to']) && !empty($options['date_to'])) {
            $conditions[] = "a.created_at <= :date_to";
            $params['date_to'] = $options['date_to'] . ' 23:59:59';
        }

        // Ajouter les conditions à la requête
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        $sql .= " GROUP BY status";

        $rows = $this->db->all($sql, $params);

        $result = [];
        foreach ($rows as $row) {
            $result[$row['status']] = $row['count'];
        }

        return $result;
    }

    /**
     * Compte les archives par catégorie
     * @param array $options Options de filtrage
     * @return array Nombre d'archives par catégorie
     */
    public function countByCategory($options = [])
    {
        $sql = "SELECT c.name, COUNT(*) as count
                FROM {$this->table} a
                LEFT JOIN categories c ON a.category_id = c.id";

        // Tableau pour les conditions WHERE
        $conditions = [];
        $params = [];

        // Filtrage par statut
        if (isset($options['status']) && !empty($options['status'])) {
            $conditions[] = "a.status = :status";
            $params['status'] = $options['status'];
        }

        // Filtrage par date de création (début)
        if (isset($options['date_from']) && !empty($options['date_from'])) {
            $conditions[] = "a.created_at >= :date_from";
            $params['date_from'] = $options['date_from'] . ' 00:00:00';
        }

        // Filtrage par date de création (fin)
        if (isset($options['date_to']) && !empty($options['date_to'])) {
            $conditions[] = "a.created_at <= :date_to";
            $params['date_to'] = $options['date_to'] . ' 23:59:59';
        }

        // Ajouter les conditions à la requête
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        $sql .= " GROUP BY a.category_id";

        $rows = $this->db->all($sql, $params);

        $result = [];
        foreach ($rows as $row) {
            $categoryName = $row['name'] ?? 'Sans catégorie';
            $result[$categoryName] = $row['count'];
        }

        return $result;
    }
}
