<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="container">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= BASE_URL ?>archives">Archives</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?= htmlspecialchars($archive['title']) ?></li>
        </ol>
    </nav>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i><?= htmlspecialchars($archive['title']) ?>
                    </h4>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h5 class="border-bottom pb-2">Informations générales</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Référence :</strong> <?= htmlspecialchars($archive['reference_number']) ?></p>
                                <p><strong>Catégorie :</strong> <?= htmlspecialchars($archive['category_name']) ?></p>
                                <p><strong>Date de création :</strong> <?= formatDate($archive['created_at'], true) ?></p>
                                <p><strong>Dernière modification :</strong> <?= formatDate($archive['updated_at'], true) ?></p>
                            </div>
                            <div class="col-md-6">
                                <p>
                                    <strong>Statut :</strong> 
                                    <?php if ($archive['status'] == 'active'): ?>
                                        <span class="badge bg-success">Actif</span>
                                    <?php elseif ($archive['status'] == 'inactive'): ?>
                                        <span class="badge bg-secondary">Inactif</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning text-dark">Archivé</span>
                                    <?php endif; ?>
                                </p>
                                <p><strong>Téléchargements :</strong> <?= $archive['download_count'] ?></p>
                                <p><strong>Taille du fichier :</strong> <?= formatFileSize($archive['file_size']) ?></p>
                                <p><strong>Type de fichier :</strong> <?= htmlspecialchars($archive['file_type']) ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5 class="border-bottom pb-2">Description</h5>
                        <div class="p-3 bg-light rounded">
                            <?= nl2br(htmlspecialchars($archive['description'])) ?>
                        </div>
                    </div>
                    
                    <?php if (!empty($metadata)): ?>
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">Métadonnées</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Clé</th>
                                            <th>Valeur</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($metadata as $meta): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($meta['meta_key']) ?></td>
                                                <td><?= htmlspecialchars($meta['meta_value']) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-file me-2"></i>Fichier
                    </h5>
                </div>
                <div class="card-body text-center">
                    <?php 
                    $fileExtension = pathinfo($archive['file_path'], PATHINFO_EXTENSION);
                    $iconClass = 'fa-file';
                    
                    switch (strtolower($fileExtension)) {
                        case 'pdf':
                            $iconClass = 'fa-file-pdf';
                            break;
                        case 'doc':
                        case 'docx':
                            $iconClass = 'fa-file-word';
                            break;
                        case 'xls':
                        case 'xlsx':
                            $iconClass = 'fa-file-excel';
                            break;
                        case 'ppt':
                        case 'pptx':
                            $iconClass = 'fa-file-powerpoint';
                            break;
                        case 'zip':
                        case 'rar':
                            $iconClass = 'fa-file-archive';
                            break;
                        case 'jpg':
                        case 'jpeg':
                        case 'png':
                        case 'gif':
                            $iconClass = 'fa-file-image';
                            break;
                        case 'txt':
                            $iconClass = 'fa-file-alt';
                            break;
                        default:
                            $iconClass = 'fa-file';
                    }
                    ?>
                    
                    <div class="mb-4">
                        <i class="fas <?= $iconClass ?> fa-5x text-primary"></i>
                        <h5 class="mt-3"><?= htmlspecialchars(basename($archive['file_path'])) ?></h5>
                    </div>
                    
                    <?php if (isLoggedIn()): ?>
                        <a href="<?= BASE_URL ?>archives/download/<?= $archive['id'] ?>" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-download me-2"></i>Télécharger
                        </a>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-lock me-2"></i>Connectez-vous pour télécharger ce fichier
                        </div>
                        <a href="<?= BASE_URL ?>login" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if (isAdmin()): ?>
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?= BASE_URL ?>archives/edit/<?= $archive['id'] ?>" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Modifier
                            </a>
                            <form action="<?= BASE_URL ?>archives/delete" method="post">
                                <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">
                                <input type="hidden" name="id" value="<?= $archive['id'] ?>">
                                <button type="submit" class="btn btn-danger w-100 delete-confirm">
                                    <i class="fas fa-trash me-2"></i>Supprimer
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>

<?php
/**
 * Formate la taille d'un fichier en unités lisibles
 * @param int $bytes Taille en octets
 * @return string Taille formatée
 */
function formatFileSize($bytes) {
    if ($bytes === 0 || $bytes === null) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>