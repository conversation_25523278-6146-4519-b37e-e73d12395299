<?php
echo "Test PHP fonctionne !<br>";
echo "URL actuelle: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "Paramètres GET: ";
print_r($_GET);
echo "<br>";

// Test du routeur
if (isset($_GET['test_route'])) {
    echo "<h2>Test du routeur</h2>";
    
    require_once 'config/config.php';
    require_once 'includes/router.php';
    
    $router = new Router();
    
    // Utiliser la réflexion pour accéder aux routes privées
    $reflection = new ReflectionClass($router);
    $routesProperty = $reflection->getProperty('routes');
    $routesProperty->setAccessible(true);
    $routes = $routesProperty->getValue($router);
    
    echo "Routes disponibles:<br>";
    foreach ($routes as $route => $config) {
        echo "- '$route' => {$config['controller']}::{$config['action']}<br>";
    }
}
?>

<h2>Tests</h2>
<a href="test.php?test_route=1">Tester le routeur</a><br>
<a href="index.php">Page d'accueil</a><br>
<a href="index.php?url=login">Test login direct</a><br>
