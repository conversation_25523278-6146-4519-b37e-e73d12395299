<?php
/**
 * Contrôleur de base dont tous les autres contrôleurs hériteront
 */
class BaseController {
    /**
     * Charge une vue
     * @param string $view Le nom de la vue à charger
     * @param array $data Les données à passer à la vue
     */
    protected function view($view, $data = []) {
        // Extraire les données pour les rendre disponibles dans la vue
        extract($data);
        
        // Chemin complet de la vue
        $viewPath = VIEWS_PATH . $view . '.php';
        
        // Vérifier si la vue existe
        if (file_exists($viewPath)) {
            // Inclure la vue directement (les templates header/footer sont inclus dans chaque vue)
            include $viewPath;
        } else {
            // Vue non trouvée
            die("Vue '$view' non trouvée");
        }
    }
    
    /**
     * Charge une vue avec le layout principal (méthode de compatibilité)
     * @param string $view Le nom de la vue à charger
     * @param array $data Les données à passer à la vue
     */
    protected function renderWithLayout($view, $data = []) {
        // Extraire les données pour les rendre disponibles dans la vue
        extract($data);
        
        // Chemin complet de la vue
        $viewPath = VIEWS_PATH . $view . '.php';
        
        // Vérifier si la vue existe
        if (file_exists($viewPath)) {
            // Démarrer la mise en tampon de sortie
            ob_start();
            
            // Inclure la vue
            include $viewPath;
            
            // Récupérer le contenu de la vue
            $content = ob_get_clean();
            
            // Inclure le layout
            include VIEWS_PATH . 'layouts/main.php';
        } else {
            // Vue non trouvée
            die("Vue '$view' non trouvée");
        }
    }
    
    /**
     * Redirige vers une URL
     * @param string $url L'URL vers laquelle rediriger
     */
    protected function redirect($url) {
        redirect($url);
    }
    
    /**
     * Définit un message flash
     * @param string $type Le type de message (success, error, info, warning)
     * @param string $message Le message à afficher
     */
    protected function setFlash($type, $message) {
        setFlashMessage($type, $message);
    }
    
    /**
     * Vérifie si l'utilisateur est connecté
     * Si non, redirige vers la page de connexion
     */
    protected function requireLogin() {
        if (!isLoggedIn()) {
            $this->setFlash('danger', 'Vous devez être connecté pour accéder à cette page');
            $this->redirect('login');
        }
    }
    
    /**
     * Vérifie si l'utilisateur est un administrateur
     * Si non, redirige vers la page d'accueil
     */
    protected function requireAdmin() {
        $this->requireLogin();
        
        if (!isAdmin()) {
            $this->setFlash('danger', 'Vous n\'avez pas les droits pour accéder à cette page');
            $this->redirect('home');
        }
    }
    
    /**
     * Vérifie les droits d'accès selon les paramètres
     * @param bool $adminRequired True si l'accès est réservé aux administrateurs
     * @param bool $loginRequired True si l'accès est réservé aux utilisateurs connectés
     */
    protected function checkAccess($adminRequired = false, $loginRequired = true) {
        if ($adminRequired) {
            $this->requireAdmin();
        } else if ($loginRequired) {
            $this->requireLogin();
        }
    }
    
    /**
     * Vérifie si la requête est une requête POST
     * @return bool True si c'est une requête POST, false sinon
     */
    protected function isPostRequest() {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }
    
    /**
     * Vérifie si le jeton CSRF est valide
     * @return bool True si le jeton est valide, false sinon
     */
    protected function validateCsrfToken() {
        if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
            $this->setFlash('error', 'Jeton CSRF invalide');
            return false;
        }
        return true;
    }
    
    /**
     * Renvoie une réponse JSON
     * @param array $data Les données à renvoyer
     * @param int $statusCode Le code d'état HTTP (par défaut: 200)
     */
    protected function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Valide les données d'un formulaire
     * @param array $rules Les règles de validation
     * @param array $data Les données à valider
     * @return array Les erreurs de validation (tableau vide si aucune erreur)
     */
    protected function validate($rules, $data) {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            // Vérifier si le champ est requis
            if (strpos($rule, 'required') !== false && (empty($data[$field]) && $data[$field] !== '0')) {
                $errors[$field] = 'Ce champ est obligatoire.';
                continue;
            }
            
            // Ignorer les champs vides non requis
            if (empty($data[$field]) && $data[$field] !== '0') {
                continue;
            }
            
            // Valider le format d'email
            if (strpos($rule, 'email') !== false && !filter_var($data[$field], FILTER_VALIDATE_EMAIL)) {
                $errors[$field] = 'Adresse email invalide.';
            }
            
            // Valider la longueur minimale
            if (preg_match('/min:([0-9]+)/', $rule, $matches)) {
                $min = (int) $matches[1];
                if (strlen($data[$field]) < $min) {
                    $errors[$field] = 'Ce champ doit contenir au moins ' . $min . ' caractères.';
                }
            }
            
            // Valider la longueur maximale
            if (preg_match('/max:([0-9]+)/', $rule, $matches)) {
                $max = (int) $matches[1];
                if (strlen($data[$field]) > $max) {
                    $errors[$field] = 'Ce champ ne doit pas dépasser ' . $max . ' caractères.';
                }
            }
            
            // Valider les nombres
            if (strpos($rule, 'numeric') !== false && !is_numeric($data[$field])) {
                $errors[$field] = 'Ce champ doit être un nombre.';
            }
            
            // Valider les dates
            if (strpos($rule, 'date') !== false && !isValidDate($data[$field])) {
                $errors[$field] = 'Date invalide. Format attendu: AAAA-MM-JJ.';
            }
            
            // Valider la confirmation de mot de passe
            if (strpos($rule, 'confirmed') !== false) {
                $confirmField = $field . '_confirmation';
                if (!isset($data[$confirmField]) || $data[$field] !== $data[$confirmField]) {
                    $errors[$field] = 'La confirmation ne correspond pas.';
                }
            }
        }
        
        return $errors;
    }
}
?>