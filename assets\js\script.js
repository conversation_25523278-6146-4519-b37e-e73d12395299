/**
 * Script principal pour l'application de gestion d'archives
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialiser les popovers Bootstrap
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Fermer automatiquement les alertes après 5 secondes
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // Confirmation de suppression
    document.querySelectorAll('.delete-confirm').forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cet élément ? Cette action est irréversible.')) {
                e.preventDefault();
            }
        });
    });
    
    // Prévisualisation des images lors du téléchargement
    var fileInput = document.getElementById('file');
    var previewContainer = document.getElementById('preview-container');
    
    if (fileInput && previewContainer) {
        fileInput.addEventListener('change', function() {
            previewContainer.innerHTML = '';
            
            if (this.files && this.files[0]) {
                var file = this.files[0];
                var fileType = file.type;
                var validImageTypes = ['image/jpeg', 'image/png', 'image/gif'];
                
                if (validImageTypes.includes(fileType)) {
                    var img = document.createElement('img');
                    img.classList.add('img-thumbnail', 'mt-2');
                    img.style.maxHeight = '200px';
                    img.file = file;
                    previewContainer.appendChild(img);
                    
                    var reader = new FileReader();
                    reader.onload = (function(aImg) { 
                        return function(e) { 
                            aImg.src = e.target.result; 
                        }; 
                    })(img);
                    reader.readAsDataURL(file);
                } else {
                    // Afficher une icône pour les autres types de fichiers
                    var icon = document.createElement('div');
                    icon.classList.add('file-preview', 'mt-2');
                    
                    var iconClass = 'fa-file';
                    if (fileType.includes('pdf')) {
                        iconClass = 'fa-file-pdf';
                    } else if (fileType.includes('word') || fileType.includes('document')) {
                        iconClass = 'fa-file-word';
                    } else if (fileType.includes('excel') || fileType.includes('sheet')) {
                        iconClass = 'fa-file-excel';
                    } else if (fileType.includes('powerpoint') || fileType.includes('presentation')) {
                        iconClass = 'fa-file-powerpoint';
                    } else if (fileType.includes('zip') || fileType.includes('archive')) {
                        iconClass = 'fa-file-archive';
                    } else if (fileType.includes('text')) {
                        iconClass = 'fa-file-alt';
                    }
                    
                    icon.innerHTML = `
                        <i class="fas ${iconClass} fa-3x text-primary"></i>
                        <p class="mt-2">${file.name} (${formatFileSize(file.size)})</p>
                    `;
                    previewContainer.appendChild(icon);
                }
            }
        });
    }
    
    // Filtres dynamiques
    var filterForm = document.getElementById('filter-form');
    if (filterForm) {
        // Réinitialiser les filtres
        var resetButton = document.getElementById('reset-filters');
        if (resetButton) {
            resetButton.addEventListener('click', function(e) {
                e.preventDefault();
                var inputs = filterForm.querySelectorAll('input:not([type=submit]), select');
                inputs.forEach(function(input) {
                    input.value = '';
                });
                filterForm.submit();
            });
        }
        
        // Soumission automatique lors du changement de valeur des filtres
        var autoSubmitInputs = filterForm.querySelectorAll('.auto-submit');
        autoSubmitInputs.forEach(function(input) {
            input.addEventListener('change', function() {
                filterForm.submit();
            });
        });
    }
    
    // Champs de date dynamiques
    var dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(function(input) {
        // Ajouter un bouton pour effacer la date
        var clearButton = document.createElement('button');
        clearButton.type = 'button';
        clearButton.className = 'btn btn-sm btn-outline-secondary ms-2';
        clearButton.innerHTML = '<i class="fas fa-times"></i>';
        clearButton.addEventListener('click', function() {
            input.value = '';
            if (input.classList.contains('auto-submit') && filterForm) {
                filterForm.submit();
            }
        });
        input.parentNode.insertBefore(clearButton, input.nextSibling);
    });
});

/**
 * Formate la taille d'un fichier en unités lisibles
 * @param {number} bytes - La taille en octets
 * @returns {string} - La taille formatée
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    var k = 1024;
    var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    var i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}