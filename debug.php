<?php
// Démarrer la session
session_start();

// Charger les configurations
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/utils.php';
require_once 'includes/router.php';

echo "<h1>Debug du routeur</h1>";

echo "<h2>Informations de la requête</h2>";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "<br>";
echo "GET parameters: ";
print_r($_GET);
echo "<br>";

echo "<h2>Test du routeur</h2>";

// Simuler différentes URLs
$testUrls = ['', 'login', 'home', 'register'];

foreach ($testUrls as $testUrl) {
    echo "<h3>Test URL: '$testUrl'</h3>";
    
    // Sauvegarder l'état actuel
    $originalGet = $_GET;
    
    // Définir l'URL de test
    $_GET['url'] = $testUrl;
    
    try {
        $router = new Router();
        
        // Utiliser la réflexion pour tester getRequestUrl
        $reflection = new ReflectionClass($router);
        $method = $reflection->getMethod('getRequestUrl');
        $method->setAccessible(true);
        $processedUrl = $method->invoke($router);
        
        echo "URL traitée: '$processedUrl'<br>";
        
        // Vérifier les routes
        $routesProperty = $reflection->getProperty('routes');
        $routesProperty->setAccessible(true);
        $routes = $routesProperty->getValue($router);
        
        if (isset($routes[$processedUrl])) {
            $route = $routes[$processedUrl];
            echo "Route trouvée: {$route['controller']}::{$route['action']}<br>";
            
            // Vérifier si le contrôleur existe
            $controllerFile = CONTROLLERS_PATH . $route['controller'] . '.php';
            if (file_exists($controllerFile)) {
                echo "Fichier contrôleur existe: OUI<br>";
                
                // Inclure le contrôleur pour vérifier la syntaxe
                require_once $controllerFile;
                
                if (class_exists($route['controller'])) {
                    echo "Classe contrôleur existe: OUI<br>";
                    
                    $controller = new $route['controller']();
                    if (method_exists($controller, $route['action'])) {
                        echo "Méthode action existe: OUI<br>";
                    } else {
                        echo "Méthode action existe: NON<br>";
                    }
                } else {
                    echo "Classe contrôleur existe: NON<br>";
                }
            } else {
                echo "Fichier contrôleur existe: NON<br>";
            }
        } else {
            echo "Route non trouvée, utilisation de la route par défaut<br>";
        }
        
    } catch (Exception $e) {
        echo "Erreur: " . $e->getMessage() . "<br>";
    }
    
    // Restaurer l'état
    $_GET = $originalGet;
    
    echo "<hr>";
}

echo "<h2>Routes disponibles</h2>";
$router = new Router();
$reflection = new ReflectionClass($router);
$routesProperty = $reflection->getProperty('routes');
$routesProperty->setAccessible(true);
$routes = $routesProperty->getValue($router);

echo "<ul>";
foreach ($routes as $route => $config) {
    echo "<li>'$route' => {$config['controller']}::{$config['action']}</li>";
}
echo "</ul>";
?>
