<?php
// Fichier de configuration principale de l'application

// Informations sur l'application
define('APP_NAME', 'Système de Gestion des Archives');
define('APP_VERSION', '1.0.0');

// Configuration des chemins
define('BASE_PATH', dirname(dirname(__FILE__)));
define('CONTROLLERS_PATH', BASE_PATH . '/controllers/');
define('MODELS_PATH', BASE_PATH . '/models/');
define('VIEWS_PATH', BASE_PATH . '/views/');
define('INCLUDES_PATH', BASE_PATH . '/includes/');
define('UPLOADS_PATH', BASE_PATH . '/uploads/');

// Configuration de l'URL
define('BASE_URL', 'http://localhost/archive/');

// Configuration des téléchargements
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10 MB
define('ALLOWED_EXTENSIONS', 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png');

// Configuration de la pagination
define('ITEMS_PER_PAGE', 20);

// Configuration des sessions
define('SESSION_LIFETIME', 3600); // 1 heure

// Configuration de la sécurité
define('HASH_COST', 10); // Coût du hachage bcrypt

// Inclure les utilitaires
require_once INCLUDES_PATH . 'utils.php';
require_once INCLUDES_PATH . 'url_helper.php';
