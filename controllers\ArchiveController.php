<?php
require_once CONTROLLERS_PATH . 'BaseController.php';
require_once MODELS_PATH . 'ArchiveModel.php';
require_once MODELS_PATH . 'CategoryModel.php';

/**
 * Contrôleur pour la gestion des archives
 */
class ArchiveController extends BaseController {
    private $archiveModel;
    private $categoryModel;
    
    /**
     * Constructeur
     */
    public function __construct() {
        parent::__construct();
        $this->archiveModel = new ArchiveModel();
        $this->categoryModel = new CategoryModel();
    }
    
    /**
     * Affiche la liste des archives
     */
    public function index() {
        // Vérifier si l'utilisateur est connecté
        $this->requireLogin();
        
        // Paramètres de pagination et de filtrage
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = ITEMS_PER_PAGE;
        $offset = ($page - 1) * $limit;
        
        // Options de filtrage
        $options = [
            'reference' => isset($_GET['reference']) ? $_GET['reference'] : '',
            'title' => isset($_GET['title']) ? $_GET['title'] : '',
            'category_id' => isset($_GET['category_id']) ? $_GET['category_id'] : '',
            'status' => isset($_GET['status']) ? $_GET['status'] : '',
            'date_from' => isset($_GET['date_from']) ? $_GET['date_from'] : '',
            'date_to' => isset($_GET['date_to']) ? $_GET['date_to'] : '',
            'order_by' => isset($_GET['order_by']) ? $_GET['order_by'] : 'created_at',
            'order_dir' => isset($_GET['order_dir']) ? $_GET['order_dir'] : 'DESC',
            'limit' => $limit,
            'offset' => $offset
        ];
        
        // Récupérer les archives
        $archives = $this->archiveModel->getAll($options);
        $totalArchives = $this->archiveModel->count($options);
        
        // Récupérer les catégories pour le filtre
        $categories = $this->categoryModel->getForDropdown(true);
        
        // Calculer la pagination
        $totalPages = ceil($totalArchives / $limit);
        
        // Charger la vue
        $this->loadView('archives/index', [
            'archives' => $archives,
            'categories' => $categories,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalArchives' => $totalArchives,
            'options' => $options
        ]);
    }
    
    /**
     * Affiche les détails d'une archive
     * @param int $id L'ID de l'archive
     */
    public function view($id) {
        // Vérifier si l'utilisateur est connecté
        $this->requireLogin();
        
        // Récupérer l'archive
        $archive = $this->archiveModel->findById($id);
        
        if (!$archive) {
            setFlashMessage('error', 'Archive non trouvée');
            redirect('archives');
            exit;
        }
        
        // Récupérer les métadonnées de l'archive
        $metadata = $this->archiveModel->getMetadata($id);
        
        // Charger la vue
        $this->loadView('archives/view', [
            'archive' => $archive,
            'metadata' => $metadata
        ]);
    }
    
    /**
     * Affiche le formulaire de création d'une archive
     */
    public function create() {
        // Vérifier si l'utilisateur est connecté
        $this->requireLogin();
        
        // Récupérer les catégories pour le formulaire
        $categories = $this->categoryModel->getForDropdown();
        
        // Charger la vue
        $this->loadView('archives/create', [
            'categories' => $categories
        ]);
    }
    
    /**
     * Traite la soumission du formulaire de création d'une archive
     */
    public function store() {
        // Vérifier si l'utilisateur est connecté
        $this->requireLogin();
        
        // Vérifier si la requête est de type POST
        $this->requirePost();
        
        // Vérifier le jeton CSRF
        $this->validateCsrfToken();
        
        // Récupérer et valider les données
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $category_id = (int)($_POST['category_id'] ?? 0);
        $status = $_POST['status'] ?? 'active';
        $metadata = $_POST['metadata'] ?? [];
        
        $errors = [];
        
        // Validation du titre
        if (empty($title)) {
            $errors['title'] = 'Le titre de l\'archive est requis';
        } elseif (strlen($title) > 255) {
            $errors['title'] = 'Le titre de l\'archive ne doit pas dépasser 255 caractères';
        }
        
        // Validation de la catégorie
        if (empty($category_id)) {
            $errors['category_id'] = 'La catégorie est requise';
        } else {
            $category = $this->categoryModel->findById($category_id);
            if (!$category) {
                $errors['category_id'] = 'Catégorie invalide';
            }
        }
        
        // Validation du statut
        if (!in_array($status, ['active', 'inactive', 'archived'])) {
            $errors['status'] = 'Le statut doit être "active", "inactive" ou "archived"';
        }
        
        // Validation du fichier
        $file = $_FILES['file'] ?? null;
        $file_path = null;
        
        if (empty($file) || $file['error'] !== UPLOAD_ERR_OK) {
            $errors['file'] = 'Le fichier est requis';
        } else {
            // Vérifier la taille du fichier
            if ($file['size'] > MAX_FILE_SIZE) {
                $errors['file'] = 'Le fichier est trop volumineux (maximum ' . (MAX_FILE_SIZE / 1024 / 1024) . ' Mo)';
            }
            
            // Vérifier l'extension du fichier
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($extension, ALLOWED_EXTENSIONS)) {
                $errors['file'] = 'Type de fichier non autorisé. Extensions autorisées : ' . implode(', ', ALLOWED_EXTENSIONS);
            }
            
            // Si pas d'erreur, déplacer le fichier
            if (empty($errors['file'])) {
                $unique_filename = generateUniqueFilename($extension);
                $upload_dir = UPLOADS_PATH;
                $file_path = $upload_dir . $unique_filename;
                
                // Créer le répertoire s'il n'existe pas
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                if (!move_uploaded_file($file['tmp_name'], $file_path)) {
                    $errors['file'] = 'Erreur lors du téléchargement du fichier';
                    $file_path = null;
                }
            }
        }
        
        // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
        if (!empty($errors)) {
            // Supprimer le fichier téléchargé s'il existe
            if ($file_path && file_exists($file_path)) {
                unlink($file_path);
            }
            
            $_SESSION['form_data'] = $_POST;
            $_SESSION['form_errors'] = $errors;
            redirect('archives/create');
            exit;
        }
        
        // Créer l'archive
        $archiveData = [
            'title' => $title,
            'description' => $description,
            'category_id' => $category_id,
            'status' => $status,
            'file_path' => $file_path,
            'file_name' => $file['name'],
            'file_size' => $file['size'],
            'file_type' => $file['type'],
            'created_by' => $_SESSION['user_id']
        ];
        
        $archiveId = $this->archiveModel->create($archiveData, $metadata);
        
        if ($archiveId) {
            // Succès
            setFlashMessage('success', 'L\'archive a été créée avec succès');
            redirect('archives/view/' . $archiveId);
        } else {
            // Erreur
            setFlashMessage('error', 'Une erreur est survenue lors de la création de l\'archive');
            $_SESSION['form_data'] = $_POST;
            redirect('archives/create');
        }
    }
    
    /**
     * Affiche le formulaire de modification d'une archive
     * @param int $id L'ID de l'archive
     */
    public function edit($id) {
        // Vérifier si l'utilisateur est connecté
        $this->requireLogin();
        
        // Récupérer l'archive
        $archive = $this->archiveModel->findById($id);
        
        if (!$archive) {
            setFlashMessage('error', 'Archive non trouvée');
            redirect('archives');
            exit;
        }
        
        // Récupérer les métadonnées de l'archive
        $metadata = $this->archiveModel->getMetadata($id);
        
        // Récupérer les catégories pour le formulaire
        $categories = $this->categoryModel->getForDropdown();
        
        // Charger la vue
        $this->loadView('archives/edit', [
            'archive' => $archive,
            'metadata' => $metadata,
            'categories' => $categories
        ]);
    }
    
    /**
     * Traite la soumission du formulaire de modification d'une archive
     * @param int $id L'ID de l'archive
     */
    public function update($id) {
        // Vérifier si l'utilisateur est connecté
        $this->requireLogin();
        
        // Vérifier si la requête est de type POST
        $this->requirePost();
        
        // Vérifier le jeton CSRF
        $this->validateCsrfToken();
        
        // Récupérer l'archive
        $archive = $this->archiveModel->findById($id);
        
        if (!$archive) {
            setFlashMessage('error', 'Archive non trouvée');
            redirect('archives');
            exit;
        }
        
        // Récupérer et valider les données
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $category_id = (int)($_POST['category_id'] ?? 0);
        $status = $_POST['status'] ?? 'active';
        $metadata = $_POST['metadata'] ?? [];
        
        $errors = [];
        
        // Validation du titre
        if (empty($title)) {
            $errors['title'] = 'Le titre de l\'archive est requis';
        } elseif (strlen($title) > 255) {
            $errors['title'] = 'Le titre de l\'archive ne doit pas dépasser 255 caractères';
        }
        
        // Validation de la catégorie
        if (empty($category_id)) {
            $errors['category_id'] = 'La catégorie est requise';
        } else {
            $category = $this->categoryModel->findById($category_id);
            if (!$category) {
                $errors['category_id'] = 'Catégorie invalide';
            }
        }
        
        // Validation du statut
        if (!in_array($status, ['active', 'inactive', 'archived'])) {
            $errors['status'] = 'Le statut doit être "active", "inactive" ou "archived"';
        }
        
        // Traitement du fichier (optionnel pour la mise à jour)
        $file = $_FILES['file'] ?? null;
        $file_path = $archive['file_path'];
        $file_name = $archive['file_name'];
        $file_size = $archive['file_size'];
        $file_type = $archive['file_type'];
        $new_file = false;
        
        if (!empty($file) && $file['error'] === UPLOAD_ERR_OK) {
            // Vérifier la taille du fichier
            if ($file['size'] > MAX_FILE_SIZE) {
                $errors['file'] = 'Le fichier est trop volumineux (maximum ' . (MAX_FILE_SIZE / 1024 / 1024) . ' Mo)';
            }
            
            // Vérifier l'extension du fichier
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($extension, ALLOWED_EXTENSIONS)) {
                $errors['file'] = 'Type de fichier non autorisé. Extensions autorisées : ' . implode(', ', ALLOWED_EXTENSIONS);
            }
            
            // Si pas d'erreur, déplacer le fichier
            if (empty($errors['file'])) {
                $unique_filename = generateUniqueFilename($extension);
                $upload_dir = UPLOADS_PATH;
                $new_file_path = $upload_dir . $unique_filename;
                
                // Créer le répertoire s'il n'existe pas
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                if (!move_uploaded_file($file['tmp_name'], $new_file_path)) {
                    $errors['file'] = 'Erreur lors du téléchargement du fichier';
                } else {
                    $new_file = true;
                    $file_path = $new_file_path;
                    $file_name = $file['name'];
                    $file_size = $file['size'];
                    $file_type = $file['type'];
                }
            }
        }
        
        // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
        if (!empty($errors)) {
            // Supprimer le nouveau fichier téléchargé s'il existe
            if ($new_file && file_exists($file_path)) {
                unlink($file_path);
            }
            
            $_SESSION['form_data'] = $_POST;
            $_SESSION['form_errors'] = $errors;
            redirect('archives/edit/' . $id);
            exit;
        }
        
        // Mettre à jour l'archive
        $archiveData = [
            'title' => $title,
            'description' => $description,
            'category_id' => $category_id,
            'status' => $status,
            'updated_by' => $_SESSION['user_id']
        ];
        
        // Ajouter les informations du fichier si un nouveau fichier a été téléchargé
        if ($new_file) {
            $archiveData['file_path'] = $file_path;
            $archiveData['file_name'] = $file_name;
            $archiveData['file_size'] = $file_size;
            $archiveData['file_type'] = $file_type;
        }
        
        $success = $this->archiveModel->update($id, $archiveData, $metadata);
        
        if ($success) {
            // Supprimer l'ancien fichier si un nouveau fichier a été téléchargé
            if ($new_file && $archive['file_path'] && file_exists($archive['file_path'])) {
                unlink($archive['file_path']);
            }
            
            // Succès
            setFlashMessage('success', 'L\'archive a été mise à jour avec succès');
            redirect('archives/view/' . $id);
        } else {
            // Erreur
            setFlashMessage('error', 'Une erreur est survenue lors de la mise à jour de l\'archive');
            $_SESSION['form_data'] = $_POST;
            redirect('archives/edit/' . $id);
        }
    }
    
    /**
     * Supprime une archive
     * @param int $id L'ID de l'archive
     */
    public function delete($id) {
        // Vérifier si l'utilisateur est connecté
        $this->requireLogin();
        
        // Vérifier si la requête est de type POST
        $this->requirePost();
        
        // Vérifier le jeton CSRF
        $this->validateCsrfToken();
        
        // Récupérer l'archive
        $archive = $this->archiveModel->findById($id);
        
        if (!$archive) {
            setFlashMessage('error', 'Archive non trouvée');
            redirect('archives');
            exit;
        }
        
        // Supprimer l'archive
        $success = $this->archiveModel->delete($id);
        
        if ($success) {
            // Supprimer le fichier
            if ($archive['file_path'] && file_exists($archive['file_path'])) {
                unlink($archive['file_path']);
            }
            
            // Succès
            setFlashMessage('success', 'L\'archive a été supprimée avec succès');
        } else {
            // Erreur
            setFlashMessage('error', 'Une erreur est survenue lors de la suppression de l\'archive');
        }
        
        redirect('archives');
    }
    
    /**
     * Télécharge le fichier d'une archive
     * @param int $id L'ID de l'archive
     */
    public function download($id) {
        // Vérifier si l'utilisateur est connecté
        $this->requireLogin();
        
        // Récupérer l'archive
        $archive = $this->archiveModel->findById($id);
        
        if (!$archive || !$archive['file_path'] || !file_exists($archive['file_path'])) {
            setFlashMessage('error', 'Fichier non trouvé');
            redirect('archives');
            exit;
        }
        
        // Mettre à jour le compteur de téléchargements
        $this->archiveModel->update($id, ['download_count' => $archive['download_count'] + 1]);
        
        // Télécharger le fichier
        header('Content-Description: File Transfer');
        header('Content-Type: ' . $archive['file_type']);
        header('Content-Disposition: attachment; filename="' . $archive['file_name'] . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($archive['file_path']));
        readfile($archive['file_path']);
        exit;
    }
    
    /**
     * Affiche le tableau de bord des statistiques des archives
     */
    public function dashboard() {
        // Vérifier si l'utilisateur est connecté
        $this->requireLogin();
        
        // Récupérer les statistiques
        $stats = [
            'total' => $this->archiveModel->countTotal(),
            'by_status' => $this->archiveModel->countByStatus(),
            'by_category' => $this->archiveModel->countByCategory(),
            'by_month' => $this->archiveModel->countByMonth()
        ];
        
        // Charger la vue
        $this->loadView('archives/dashboard', ['stats' => $stats]);
    }
}
?>