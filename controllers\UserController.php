<?php

/**
 * Contrôleur pour la gestion des utilisateurs
 */
require_once MODELS_PATH . 'UserModel.php';

class UserController extends BaseController
{
    private $userModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new UserModel();
    }

    /**
     * Affiche la liste des utilisateurs (admin uniquement)
     */
    public function index()
    {
        $this->requireAdmin();

        $users = $this->userModel->getAll();
        $this->render('users/index', [
            'users' => $users,
            'title' => 'Gestion des utilisateurs'
        ]);
    }

    /**
     * Affiche le formulaire de création d'utilisateur
     */
    public function create()
    {
        $this->requireAdmin();

        $this->render('users/create', [
            'title' => 'Créer un utilisateur'
        ]);
    }

    /**
     * Enregistre un nouvel utilisateur
     */
    public function store()
    {
        $this->requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'name' => $_POST['username'] ?? '',
                'email' => $_POST['email'] ?? '',
                'password' => $_POST['password'] ?? '',
                'role' => $_POST['role'] ?? 'user'
            ];

            // Validation
            $errors = $this->validateUserData($data);

            if (empty($errors)) {
                // Hash the password before creating user
                $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);

                if ($this->userModel->create($data)) {
                    $this->setFlashMessage('Utilisateur créé avec succès', 'success');
                    $this->redirect('users');
                } else {
                    $this->setFlashMessage('Erreur lors de la création de l\'utilisateur', 'error');
                }
            } else {
                $this->render('users/create', [
                    'errors' => $errors,
                    'data' => $data,
                    'title' => 'Créer un utilisateur'
                ]);
                return;
            }
        }

        $this->redirect('users/create');
    }

    /**
     * Affiche les détails d'un utilisateur
     */
    public function show()
    {
        $this->requireAdmin();

        $id = $_GET['id'] ?? 0;
        $user = $this->userModel->findById($id);

        if (!$user) {
            $this->setFlashMessage('Utilisateur non trouvé', 'error');
            $this->redirect('users');
        }

        $this->render('users/show', [
            'user' => $user,
            'title' => 'Détails de l\'utilisateur'
        ]);
    }

    /**
     * Affiche le formulaire d'édition d'utilisateur
     */
    public function edit()
    {
        $this->requireAdmin();

        $id = $_GET['id'] ?? 0;
        $user = $this->userModel->findById($id);

        if (!$user) {
            $this->setFlashMessage('Utilisateur non trouvé', 'error');
            $this->redirect('users');
        }

        $this->render('users/edit', [
            'user' => $user,
            'title' => 'Modifier l\'utilisateur'
        ]);
    }

    /**
     * Met à jour un utilisateur
     */
    public function update()
    {
        $this->requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $id = $_POST['id'] ?? 0;
            $data = [
                'name' => $_POST['username'] ?? '',
                'email' => $_POST['email'] ?? '',
                'role' => $_POST['role'] ?? 'user'
            ];

            // Ajouter le mot de passe seulement s'il est fourni
            if (!empty($_POST['password'])) {
                $data['password'] = $_POST['password'];
            }

            // Validation
            $errors = $this->validateUserData($data, $id);

            if (empty($errors)) {
                // Hash the password if provided
                if (isset($data['password'])) {
                    $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
                }

                if ($this->userModel->update($id, $data)) {
                    $this->setFlashMessage('Utilisateur mis à jour avec succès', 'success');
                    $this->redirect('users');
                } else {
                    $this->setFlashMessage('Erreur lors de la mise à jour de l\'utilisateur', 'error');
                }
            } else {
                $user = $this->userModel->findById($id);
                $this->render('users/edit', [
                    'user' => $user,
                    'errors' => $errors,
                    'title' => 'Modifier l\'utilisateur'
                ]);
                return;
            }
        }

        $this->redirect('users');
    }

    /**
     * Supprime un utilisateur
     */
    public function delete()
    {
        $this->requireAdmin();

        $id = $_GET['id'] ?? 0;

        // Empêcher la suppression de son propre compte
        if ($id == $_SESSION['user_id']) {
            $this->setFlashMessage('Vous ne pouvez pas supprimer votre propre compte', 'error');
            $this->redirect('users');
        }

        if ($this->userModel->delete($id)) {
            $this->setFlashMessage('Utilisateur supprimé avec succès', 'success');
        } else {
            $this->setFlashMessage('Erreur lors de la suppression de l\'utilisateur', 'error');
        }

        $this->redirect('users');
    }

    /**
     * Affiche le profil de l'utilisateur connecté
     */
    public function profile()
    {
        $this->requireAuth();

        $user = $this->userModel->findById($_SESSION['user_id']);

        $this->render('users/profile', [
            'user' => $user,
            'title' => 'Mon profil'
        ]);
    }

    /**
     * Met à jour le profil de l'utilisateur connecté
     */
    public function updateProfile()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'name' => $_POST['username'] ?? '',
                'email' => $_POST['email'] ?? ''
            ];

            // Ajouter le mot de passe seulement s'il est fourni
            if (!empty($_POST['password'])) {
                $data['password'] = $_POST['password'];
            }

            // Validation
            $errors = $this->validateUserData($data, $_SESSION['user_id']);

            if (empty($errors)) {
                // Hash the password if provided
                if (isset($data['password'])) {
                    $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
                }

                if ($this->userModel->update($_SESSION['user_id'], $data)) {
                    $this->setFlashMessage('Profil mis à jour avec succès', 'success');
                } else {
                    $this->setFlashMessage('Erreur lors de la mise à jour du profil', 'error');
                }
            } else {
                $user = $this->userModel->findById($_SESSION['user_id']);
                $this->render('users/profile', [
                    'user' => $user,
                    'errors' => $errors,
                    'title' => 'Mon profil'
                ]);
                return;
            }
        }

        $this->redirect('profile');
    }

    /**
     * Valide les données d'utilisateur
     */
    private function validateUserData($data, $userId = null)
    {
        $errors = [];

        // Validation du nom d'utilisateur
        if (empty($data['name'])) {
            $errors['username'] = 'Le nom d\'utilisateur est requis';
        } elseif (strlen($data['name']) < 3) {
            $errors['username'] = 'Le nom d\'utilisateur doit contenir au moins 3 caractères';
        } elseif ($this->nameExists($data['name'], $userId)) {
            $errors['username'] = 'Ce nom d\'utilisateur est déjà utilisé';
        }

        // Validation de l'email
        if (empty($data['email'])) {
            $errors['email'] = 'L\'email est requis';
        } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'L\'email n\'est pas valide';
        } elseif ($this->userModel->emailExists($data['email'], $userId)) {
            $errors['email'] = 'Cet email est déjà utilisé';
        }

        // Validation du mot de passe (seulement si fourni)
        if (isset($data['password']) && !empty($data['password'])) {
            if (strlen($data['password']) < 6) {
                $errors['password'] = 'Le mot de passe doit contenir au moins 6 caractères';
            }
        } elseif ($userId === null) {
            // Mot de passe requis pour la création
            $errors['password'] = 'Le mot de passe est requis';
        }

        return $errors;
    }

    /**
     * Vérifie si un nom d'utilisateur existe déjà
     */
    private function nameExists($name, $excludeId = null)
    {
        $users = $this->userModel->getAll();
        foreach ($users as $user) {
            if ($user['name'] === $name && ($excludeId === null || $user['id'] != $excludeId)) {
                return true;
            }
        }
        return false;
    }
}
