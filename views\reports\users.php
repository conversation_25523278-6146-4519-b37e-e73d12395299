<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= BASE_URL ?>">Accueil</a></li>
                    <li class="breadcrumb-item"><a href="<?= BASE_URL ?>reports">Rapports</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Utilisateurs</li>
                </ol>
            </nav>
            
            <h1 class="mb-4"><i class="fas fa-users me-2"></i>Rapport des Utilisateurs</h1>
            
            <!-- Filtres -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0"><i class="fas fa-filter me-2"></i>Filtres</h3>
                </div>
                <div class="card-body">
                    <form action="<?= BASE_URL ?>reports/users" method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Date de début</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="<?= htmlspecialchars($options['date_from'] ?? date('Y-m-d', strtotime('-1 month'))) ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">Date de fin</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="<?= htmlspecialchars($options['date_to'] ?? date('Y-m-d')) ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="role" class="form-label">Rôle</label>
                            <select class="form-select" id="role" name="role">
                                <option value="">Tous les rôles</option>
                                <option value="admin" <?= ($options['role'] == 'admin') ? 'selected' : '' ?>>Administrateur</option>
                                <option value="user" <?= ($options['role'] == 'user') ? 'selected' : '' ?>>Utilisateur</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="status" class="form-label">Statut</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Tous les statuts</option>
                                <option value="active" <?= ($options['status'] == 'active') ? 'selected' : '' ?>>Actif</option>
                                <option value="inactive" <?= ($options['status'] == 'inactive') ? 'selected' : '' ?>>Inactif</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Format d'exportation</label>
                            <div class="d-flex">
                                <a href="<?= BASE_URL ?>reports/users?format=csv<?= http_build_query(array_filter($options)) ?>" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-file-csv me-2"></i>Exporter en CSV
                                </a>
                                <a href="<?= BASE_URL ?>reports/users?format=pdf<?= http_build_query(array_filter($options)) ?>" class="btn btn-outline-danger">
                                    <i class="fas fa-file-pdf me-2"></i>Exporter en PDF
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6 d-flex align-items-end justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Appliquer les filtres
                            </button>
                            <a href="<?= BASE_URL ?>reports/users" class="btn btn-secondary ms-2">
                                <i class="fas fa-undo me-2"></i>Réinitialiser
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Statistiques générales -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white h-100">
                        <div class="card-body text-center">
                            <h3 class="display-4"><?= number_format($stats['total'], 0, ',', ' ') ?></h3>
                            <p class="card-text">Utilisateurs au total</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white h-100">
                        <div class="card-body text-center">
                            <h3 class="display-4"><?= number_format($stats['by_role']['admin'], 0, ',', ' ') ?></h3>
                            <p class="card-text">Administrateurs</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white h-100">
                        <div class="card-body text-center">
                            <h3 class="display-4"><?= number_format($stats['by_role']['user'], 0, ',', ' ') ?></h3>
                            <p class="card-text">Utilisateurs standard</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-dark h-100">
                        <div class="card-body text-center">
                            <h3 class="display-4"><?= number_format($stats['by_status']['active'], 0, ',', ' ') ?></h3>
                            <p class="card-text">Utilisateurs actifs</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Graphiques -->
            <div class="row mb-4">
                <!-- Graphique par rôle -->
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-header">
                            <h3 class="card-title">Utilisateurs par rôle</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="roleChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Graphique par statut -->
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-header">
                            <h3 class="card-title">Utilisateurs par statut</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="statusChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Utilisateurs les plus actifs -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0"><i class="fas fa-trophy me-2"></i>Utilisateurs les plus actifs</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Utilisateur</th>
                                    <th>Email</th>
                                    <th>Archives créées</th>
                                    <th>Dernière connexion</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($users)): ?>
                                    <?php 
                                    // Trier les utilisateurs par nombre d'archives créées
                                    usort($users, function($a, $b) {
                                        return $b['archives_count'] - $a['archives_count'];
                                    });
                                    
                                    // Afficher les 5 premiers utilisateurs
                                    $topUsers = array_slice($users, 0, 5);
                                    foreach ($topUsers as $user): 
                                    ?>
                                        <tr>
                                            <td>
                                                <?= htmlspecialchars($user['name']) ?>
                                                <?php if ($user['role'] == 'admin'): ?>
                                                    <span class="badge bg-success">Admin</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= htmlspecialchars($user['email']) ?></td>
                                            <td><?= number_format($user['archives_count'], 0, ',', ' ') ?></td>
                                            <td><?= $user['last_login'] ? formatDate($user['last_login']) : 'Jamais' ?></td>
                                            <td>
                                                <a href="<?= BASE_URL ?>users/view/<?= $user['id'] ?>" class="btn btn-sm btn-primary" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center">Aucun utilisateur trouvé.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Tableau des utilisateurs -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0"><i class="fas fa-table me-2"></i>Liste des utilisateurs</h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($users)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Nom</th>
                                        <th>Email</th>
                                        <th>Rôle</th>
                                        <th>Statut</th>
                                        <th>Date d'inscription</th>
                                        <th>Dernière connexion</th>
                                        <th>Archives</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td><?= $user['id'] ?></td>
                                            <td><?= htmlspecialchars($user['name']) ?></td>
                                            <td><?= htmlspecialchars($user['email']) ?></td>
                                            <td>
                                                <span class="badge <?= $user['role'] == 'admin' ? 'bg-success' : 'bg-info' ?>">
                                                    <?= $user['role'] == 'admin' ? 'Administrateur' : 'Utilisateur' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge <?= $user['status'] == 'active' ? 'bg-success' : 'bg-secondary' ?>">
                                                    <?= $user['status'] == 'active' ? 'Actif' : 'Inactif' ?>
                                                </span>
                                            </td>
                                            <td><?= formatDate($user['created_at']) ?></td>
                                            <td><?= $user['last_login'] ? formatDate($user['last_login']) : 'Jamais' ?></td>
                                            <td><?= number_format($user['archives_count'], 0, ',', ' ') ?></td>
                                            <td>
                                                <a href="<?= BASE_URL ?>users/view/<?= $user['id'] ?>" class="btn btn-sm btn-primary" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= BASE_URL ?>users/edit/<?= $user['id'] ?>" class="btn btn-sm btn-warning" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Aucun utilisateur trouvé pour les critères sélectionnés.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Graphique par rôle
    document.addEventListener('DOMContentLoaded', function() {
        const roleCtx = document.getElementById('roleChart').getContext('2d');
        const roleData = {
            labels: ['Administrateurs', 'Utilisateurs standard'],
            datasets: [{
                label: 'Nombre d\'utilisateurs',
                data: [<?= $stats['by_role']['admin'] ?>, <?= $stats['by_role']['user'] ?>],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.7)',  // vert pour admin
                    'rgba(23, 162, 184, 0.7)'  // bleu pour user
                ],
                borderWidth: 1
            }]
        };
        
        new Chart(roleCtx, {
            type: 'pie',
            data: roleData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    });
    
    // Graphique par statut
    document.addEventListener('DOMContentLoaded', function() {
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusData = {
            labels: ['Actifs', 'Inactifs'],
            datasets: [{
                label: 'Nombre d\'utilisateurs',
                data: [<?= $stats['by_status']['active'] ?>, <?= $stats['by_status']['inactive'] ?>],
                backgroundColor: [
                    'rgba(255, 193, 7, 0.7)',  // jaune pour actif
                    'rgba(108, 117, 125, 0.7)'  // gris pour inactif
                ],
                borderWidth: 1
            }]
        };
        
        new Chart(statusCtx, {
            type: 'doughnut',
            data: statusData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    });
</script>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>