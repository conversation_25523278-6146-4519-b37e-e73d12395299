<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Connexion</h4>
            </div>
            <div class="card-body">
                <form action="<?= BASE_URL ?>authenticate" method="post">
                    <!-- Jeton CSRF -->
                    <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email"
                            class="form-control <?= isset($_SESSION['form_errors']['email']) ? 'is-invalid' : '' ?>"
                            id="email" name="email"
                            value="<?= htmlspecialchars($_SESSION['form_data']['email'] ?? '') ?>" required>
                        <?php if (isset($_SESSION['form_errors']['email'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['email']) ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Mot de passe</label>
                        <input type="password"
                            class="form-control <?= isset($_SESSION['form_errors']['password']) ? 'is-invalid' : '' ?>"
                            id="password" name="password" required>
                        <?php if (isset($_SESSION['form_errors']['password'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['password']) ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
                        <label class="form-check-label" for="remember">Se souvenir de moi</label>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                        </button>
                    </div>
                </form>

                <div class="mt-3 text-center">
                    <p>
                        <a href="<?= BASE_URL ?>forgot-password">Mot de passe oublié ?</a>
                    </p>
                    <p>
                        Pas encore de compte ? <a href="<?= BASE_URL ?>register">S'inscrire</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
// Nettoyer les données de formulaire et les erreurs après l'affichage
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);
?>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>