/* Styles principaux pour l'application de gestion d'archives */

/* Variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Styles généraux */
body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f5f5f5;
}

main {
    flex: 1;
}

/* En-tête */
.navbar-brand {
    font-weight: bold;
}

/* Pied de page */
footer {
    margin-top: auto;
}

/* Cartes */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    font-weight: bold;
}

/* Tableaux */
.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

.table th {
    background-color: #f8f9fa;
}

/* Formulaires */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Boutons */
.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
}

/* Badges */
.badge-status {
    font-size: 0.85em;
    padding: 0.35em 0.65em;
}

.badge-active {
    background-color: var(--success-color);
    color: white;
}

.badge-inactive {
    background-color: var(--secondary-color);
    color: white;
}

.badge-archived {
    background-color: var(--warning-color);
    color: black;
}

/* Alertes */
.alert {
    margin-bottom: 1.5rem;
}

/* Pagination */
.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link {
    color: var(--primary-color);
}

.pagination .page-link:hover {
    background-color: #e9ecef;
}

/* Vignettes de fichiers */
.file-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: white;
    transition: all 0.2s ease-in-out;
}

.file-thumbnail:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.file-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.file-name {
    text-align: center;
    word-break: break-word;
    font-size: 0.875rem;
}

/* Statistiques */
.stat-card {
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.8;
}

/* Filtres de recherche */
.filter-section {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-icon {
        font-size: 2rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
}