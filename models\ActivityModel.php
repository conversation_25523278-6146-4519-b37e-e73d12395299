<?php
require_once CONFIG_PATH . 'database.php';

/**
 * Modèle pour la gestion des activités utilisateurs
 */
class ActivityModel {
    private $db;
    private $table = 'activities';
    
    /**
     * Constructeur
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Récupère toutes les activités
     * @param array $options Options de filtrage et de pagination
     * @return array Les activités
     */
    public function getAll($options = []) {
        // Construire la requête SQL de base
        $sql = "SELECT a.*, u.username 
                FROM {$this->table} a 
                LEFT JOIN users u ON a.user_id = u.id";
        
        // Tableau pour les conditions WHERE
        $conditions = [];
        $params = [];
        
        // Filtrage par utilisateur
        if (isset($options['user_id']) && !empty($options['user_id'])) {
            $conditions[] = "a.user_id = :user_id";
            $params['user_id'] = $options['user_id'];
        }
        
        // Filtrage par type d'action
        if (isset($options['action']) && !empty($options['action'])) {
            $conditions[] = "a.action_type = :action_type";
            $params['action_type'] = $options['action'];
        }
        
        // Filtrage par date (début)
        if (isset($options['date_from']) && !empty($options['date_from'])) {
            $conditions[] = "a.created_at >= :date_from";
            $params['date_from'] = $options['date_from'] . ' 00:00:00';
        }
        
        // Filtrage par date (fin)
        if (isset($options['date_to']) && !empty($options['date_to'])) {
            $conditions[] = "a.created_at <= :date_to";
            $params['date_to'] = $options['date_to'] . ' 23:59:59';
        }
        
        // Ajouter les conditions à la requête
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }
        
        // Tri
        $sql .= " ORDER BY " . ($options['order_by'] ?? 'a.created_at') . " " . ($options['order_dir'] ?? 'DESC');
        
        // Pagination
        if (isset($options['limit']) && isset($options['offset'])) {
            $sql .= " LIMIT :offset, :limit";
            $params['limit'] = $options['limit'];
            $params['offset'] = $options['offset'];
        } elseif (isset($options['limit'])) {
            $sql .= " LIMIT :limit";
            $params['limit'] = $options['limit'];
        }
        
        // Exécuter la requête
        return $this->db->all($sql, $params);
    }
    
    /**
     * Compte le nombre total d'activités selon les filtres
     * @param array $options Options de filtrage
     * @return int Le nombre total d'activités
     */
    public function countAll($options = []) {
        // Construire la requête SQL de base
        $sql = "SELECT COUNT(*) as total FROM {$this->table} a";
        
        // Tableau pour les conditions WHERE
        $conditions = [];
        $params = [];
        
        // Filtrage par utilisateur
        if (isset($options['user_id']) && !empty($options['user_id'])) {
            $conditions[] = "a.user_id = :user_id";
            $params['user_id'] = $options['user_id'];
        }
        
        // Filtrage par type d'action
        if (isset($options['action']) && !empty($options['action'])) {
            $conditions[] = "a.action_type = :action_type";
            $params['action_type'] = $options['action'];
        }
        
        // Filtrage par date (début)
        if (isset($options['date_from']) && !empty($options['date_from'])) {
            $conditions[] = "a.created_at >= :date_from";
            $params['date_from'] = $options['date_from'] . ' 00:00:00';
        }
        
        // Filtrage par date (fin)
        if (isset($options['date_to']) && !empty($options['date_to'])) {
            $conditions[] = "a.created_at <= :date_to";
            $params['date_to'] = $options['date_to'] . ' 23:59:59';
        }
        
        // Ajouter les conditions à la requête
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }
        
        // Exécuter la requête
        $result = $this->db->one($sql, $params);
        return $result['total'] ?? 0;
    }
    
    /**
     * Récupère une activité par son ID
     * @param int $id L'ID de l'activité
     * @return array|false L'activité ou false si non trouvée
     */
    public function getById($id) {
        $sql = "SELECT a.*, u.username 
                FROM {$this->table} a 
                LEFT JOIN users u ON a.user_id = u.id 
                WHERE a.id = :id";
        
        return $this->db->one($sql, ['id' => $id]);
    }
    
    /**
     * Enregistre une nouvelle activité
     * @param array $data Les données de l'activité
     * @return int|false L'ID de l'activité créée ou false en cas d'erreur
     */
    public function log($data) {
        $sql = "INSERT INTO {$this->table} (user_id, action_type, entity_type, entity_id, description, ip_address, created_at) 
                VALUES (:user_id, :action_type, :entity_type, :entity_id, :description, :ip_address, NOW())";
        
        $params = [
            'user_id' => $data['user_id'] ?? null,
            'action_type' => $data['action_type'],
            'entity_type' => $data['entity_type'] ?? null,
            'entity_id' => $data['entity_id'] ?? null,
            'description' => $data['description'],
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0'
        ];
        
        $this->db->query($sql, $params);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * Récupère les statistiques d'activités par type d'action
     * @param array $options Options de filtrage
     * @return array Les statistiques par type d'action
     */
    public function getStatsByActionType($options = []) {
        // Construire la requête SQL de base
        $sql = "SELECT action_type, COUNT(*) as count 
                FROM {$this->table} a";
        
        // Tableau pour les conditions WHERE
        $conditions = [];
        $params = [];
        
        // Filtrage par utilisateur
        if (isset($options['user_id']) && !empty($options['user_id'])) {
            $conditions[] = "a.user_id = :user_id";
            $params['user_id'] = $options['user_id'];
        }
        
        // Filtrage par date (début)
        if (isset($options['date_from']) && !empty($options['date_from'])) {
            $conditions[] = "a.created_at >= :date_from";
            $params['date_from'] = $options['date_from'] . ' 00:00:00';
        }
        
        // Filtrage par date (fin)
        if (isset($options['date_to']) && !empty($options['date_to'])) {
            $conditions[] = "a.created_at <= :date_to";
            $params['date_to'] = $options['date_to'] . ' 23:59:59';
        }
        
        // Ajouter les conditions à la requête
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }
        
        $sql .= " GROUP BY action_type ORDER BY count DESC";
        
        // Exécuter la requête
        return $this->db->all($sql, $params);
    }
    
    /**
     * Récupère les statistiques d'activités par jour
     * @param array $options Options de filtrage
     * @return array Les statistiques par jour
     */
    public function getStatsByDay($options = []) {
        // Construire la requête SQL de base
        $sql = "SELECT DATE(created_at) as day, COUNT(*) as count 
                FROM {$this->table} a";
        
        // Tableau pour les conditions WHERE
        $conditions = [];
        $params = [];
        
        // Filtrage par utilisateur
        if (isset($options['user_id']) && !empty($options['user_id'])) {
            $conditions[] = "a.user_id = :user_id";
            $params['user_id'] = $options['user_id'];
        }
        
        // Filtrage par type d'action
        if (isset($options['action']) && !empty($options['action'])) {
            $conditions[] = "a.action_type = :action_type";
            $params['action_type'] = $options['action'];
        }
        
        // Filtrage par date (début)
        if (isset($options['date_from']) && !empty($options['date_from'])) {
            $conditions[] = "a.created_at >= :date_from";
            $params['date_from'] = $options['date_from'] . ' 00:00:00';
        }
        
        // Filtrage par date (fin)
        if (isset($options['date_to']) && !empty($options['date_to'])) {
            $conditions[] = "a.created_at <= :date_to";
            $params['date_to'] = $options['date_to'] . ' 23:59:59';
        }
        
        // Ajouter les conditions à la requête
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }
        
        $sql .= " GROUP BY DATE(created_at) ORDER BY day ASC";
        
        // Exécuter la requête
        return $this->db->all($sql, $params);
    }
    
    /**
     * Récupère les utilisateurs les plus actifs
     * @param array $options Options de filtrage
     * @param int $limit Nombre maximum d'utilisateurs à retourner
     * @return array Les utilisateurs les plus actifs
     */
    public function getMostActiveUsers($options = [], $limit = 5) {
        // Construire la requête SQL de base
        $sql = "SELECT a.user_id, u.username, COUNT(*) as activity_count 
                FROM {$this->table} a 
                JOIN users u ON a.user_id = u.id";
        
        // Tableau pour les conditions WHERE
        $conditions = [];
        $params = [];
        
        // Filtrage par type d'action
        if (isset($options['action']) && !empty($options['action'])) {
            $conditions[] = "a.action_type = :action_type";
            $params['action_type'] = $options['action'];
        }
        
        // Filtrage par date (début)
        if (isset($options['date_from']) && !empty($options['date_from'])) {
            $conditions[] = "a.created_at >= :date_from";
            $params['date_from'] = $options['date_from'] . ' 00:00:00';
        }
        
        // Filtrage par date (fin)
        if (isset($options['date_to']) && !empty($options['date_to'])) {
            $conditions[] = "a.created_at <= :date_to";
            $params['date_to'] = $options['date_to'] . ' 23:59:59';
        }
        
        // Ajouter les conditions à la requête
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }
        
        $sql .= " GROUP BY a.user_id, u.username ORDER BY activity_count DESC LIMIT :limit";
        $params['limit'] = $limit;
        
        // Exécuter la requête
        return $this->db->all($sql, $params);
    }
}
?>