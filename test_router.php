<?php
// Test du routeur
require_once 'config/config.php';
require_once 'includes/router.php';

// Simuler une requête GET
$_GET['url'] = 'login';

echo "URL demandée: " . ($_GET['url'] ?? 'vide') . "\n";

// Créer le routeur
$router = new Router();

// Tester la méthode getRequestUrl
$reflection = new ReflectionClass($router);
$method = $reflection->getMethod('getRequestUrl');
$method->setAccessible(true);
$url = $method->invoke($router);

echo "URL traitée par le routeur: '$url'\n";

// Vérifier les routes
$routesProperty = $reflection->getProperty('routes');
$routesProperty->setAccessible(true);
$routes = $routesProperty->getValue($router);

echo "Routes disponibles:\n";
foreach ($routes as $route => $config) {
    echo "  '$route' => {$config['controller']}::{$config['action']}\n";
}

echo "\nRoute 'login' existe: " . (isset($routes['login']) ? 'OUI' : 'NON') . "\n";

if (isset($routes['login'])) {
    echo "Contrôleur pour 'login': " . $routes['login']['controller'] . "\n";
    echo "Action pour 'login': " . $routes['login']['action'] . "\n";
}
?>
