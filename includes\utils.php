<?php
/**
 * Fichier contenant des fonctions utilitaires pour l'application de gestion d'archives
 */

/**
 * Redirige vers une URL spécifiée
 * @param string $url L'URL vers laquelle rediriger
 */
function redirect($url) {
    header("Location: " . BASE_URL . $url);
    exit;
}

/**
 * Ajoute un message flash en session
 * @param string $type Le type de message (success, danger, warning, info)
 * @param string $message Le contenu du message
 */
function setFlashMessage($type, $message) {
    $_SESSION['flash_messages'][] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Récupère et supprime les messages flash de la session
 * @return array Les messages flash
 */
function getFlashMessages() {
    $messages = $_SESSION['flash_messages'] ?? [];
    unset($_SESSION['flash_messages']);
    return $messages;
}

/**
 * Récupère et supprime le message flash (compatibilité avec l'ancien système)
 * @return array|null Le message flash ou null s'il n'y en a pas
 */
function getFlashMessage() {
    if (isset($_SESSION['flash'])) {
        $flash = $_SESSION['flash'];
        unset($_SESSION['flash']);
        return $flash;
    }
    return null;
}

/**
 * Vérifie si des messages flash existent en session
 * @return bool True si des messages flash existent, false sinon
 */
function hasFlashMessage() {
    return isset($_SESSION['flash_messages']) && !empty($_SESSION['flash_messages']);
}

/**
 * Vérifie si l'utilisateur est connecté
 * @return bool True si l'utilisateur est connecté, false sinon
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Vérifie si l'utilisateur est un administrateur
 * @return bool True si l'utilisateur est un administrateur, false sinon
 */
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Nettoie une chaîne de caractères
 * @param string $data La chaîne à nettoyer
 * @return string La chaîne nettoyée
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Génère un jeton CSRF
 * @return string Le jeton CSRF
 */
function generateCsrfToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Vérifie si le jeton CSRF est valide
 * @param string $token Le jeton à vérifier
 * @return bool True si le jeton est valide, false sinon
 */
function verifyCsrfToken($token) {
    if (!isset($_SESSION['csrf_token']) || $token !== $_SESSION['csrf_token']) {
        return false;
    }
    return true;
}

/**
 * Formate une date au format français
 * @param string $date La date à formater (format MySQL)
 * @param bool $withTime Inclure l'heure dans le format
 * @return string La date formatée
 */
function formatDate($date, $withTime = false) {
    if (empty($date)) return '';
    
    $timestamp = strtotime($date);
    if ($withTime) {
        return date('d/m/Y à H:i', $timestamp);
    } else {
        return date('d/m/Y', $timestamp);
    }
}

/**
 * Tronque un texte à une longueur spécifiée
 * @param string $text Le texte à tronquer
 * @param int $length La longueur maximale
 * @param string $suffix Le suffixe à ajouter (par défaut: ...)
 * @return string Le texte tronqué
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . $suffix;
}

/**
 * Génère une chaîne aléatoire
 * @param int $length La longueur de la chaîne
 * @return string La chaîne aléatoire
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $randomString;
}

/**
 * Vérifie si une chaîne est une date valide
 * @param string $date La date à vérifier
 * @param string $format Le format attendu (par défaut: Y-m-d)
 * @return bool True si la date est valide, false sinon
 */
function isValidDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * Obtient l'extension d'un fichier
 * @param string $filename Le nom du fichier
 * @return string L'extension du fichier
 */
function getFileExtension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * Vérifie si un fichier a une extension autorisée
 * @param string $filename Le nom du fichier
 * @param string $allowedExtensions Les extensions autorisées (séparées par des virgules)
 * @return bool True si l'extension est autorisée, false sinon
 */
function hasAllowedExtension($filename, $allowedExtensions = ALLOWED_EXTENSIONS) {
    $ext = getFileExtension($filename);
    $allowed = explode(',', $allowedExtensions);
    return in_array($ext, $allowed);
}

/**
 * Génère un nom de fichier unique
 * @param string $filename Le nom du fichier original
 * @return string Le nouveau nom de fichier
 */
function generateUniqueFilename($filename) {
    $ext = getFileExtension($filename);
    return uniqid() . '_' . time() . '.' . $ext;
}
/**
 * Génère un numéro de référence unique pour les archives
 * @param string $prefix Préfixe à utiliser (par défaut 'ARC')
 * @return string Le numéro de référence généré
 */
function generateReferenceNumber($prefix = 'ARC') {
    $timestamp = time();
    $random = mt_rand(1000, 9999);
    return $prefix . '-' . date('Ymd', $timestamp) . '-' . $random;
}

/**
 * Formate une taille de fichier en unités lisibles (Ko, Mo, Go)
 * @param int $bytes La taille en octets
 * @param int $precision La précision décimale
 * @return string La taille formatée
 */
function formatFileSize($bytes, $precision = 2) {
    $units = ['o', 'Ko', 'Mo', 'Go', 'To'];
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

/**
 * Génère une icône en fonction du type MIME d'un fichier
 * @param string $mimeType Le type MIME du fichier
 * @return string La classe d'icône Font Awesome correspondante
 */
function getFileIcon($mimeType) {
    $icons = [
        'image/' => 'fa-file-image',
        'audio/' => 'fa-file-audio',
        'video/' => 'fa-file-video',
        'application/pdf' => 'fa-file-pdf',
        'application/msword' => 'fa-file-word',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'fa-file-word',
        'application/vnd.ms-excel' => 'fa-file-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'fa-file-excel',
        'application/vnd.ms-powerpoint' => 'fa-file-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'fa-file-powerpoint',
        'application/zip' => 'fa-file-archive',
        'application/x-rar-compressed' => 'fa-file-archive',
        'application/x-7z-compressed' => 'fa-file-archive',
        'text/plain' => 'fa-file-alt',
        'text/html' => 'fa-file-code',
        'application/json' => 'fa-file-code',
        'application/xml' => 'fa-file-code',
    ];
    
    foreach ($icons as $type => $icon) {
        if (strpos($mimeType, $type) === 0) {
            return $icon;
        }
    }
    
    return 'fa-file'; // Icône par défaut
}

/**
 * Vérifie si une requête est une requête AJAX
 * @return bool True si c'est une requête AJAX, false sinon
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Envoie une réponse JSON et termine le script
 * @param mixed $data Les données à envoyer
 * @param int $statusCode Le code de statut HTTP
 */
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

/**
 * Génère un slug à partir d'une chaîne de caractères
 * @param string $string La chaîne à convertir en slug
 * @return string Le slug généré
 */
function generateSlug($string) {
    // Remplacer les caractères non alphanumériques par des tirets
    $slug = preg_replace('/[^a-z0-9]+/i', '-', strtolower(trim($string)));
    // Supprimer les tirets en début et fin de chaîne
    $slug = trim($slug, '-');
    return $slug;
}
?>