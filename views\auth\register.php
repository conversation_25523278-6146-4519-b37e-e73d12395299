<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Inscription</h4>
            </div>
            <div class="card-body">
                <form action="<?= BASE_URL ?>store" method="post">
                    <!-- Jeton CSRF -->
                    <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">

                    <div class="mb-3">
                        <label for="name" class="form-label">Nom complet</label>
                        <input type="text"
                            class="form-control <?= isset($_SESSION['form_errors']['name']) ? 'is-invalid' : '' ?>"
                            id="name" name="name" value="<?= htmlspecialchars($_SESSION['form_data']['name'] ?? '') ?>"
                            required>
                        <?php if (isset($_SESSION['form_errors']['name'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['name']) ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email"
                            class="form-control <?= isset($_SESSION['form_errors']['email']) ? 'is-invalid' : '' ?>"
                            id="email" name="email"
                            value="<?= htmlspecialchars($_SESSION['form_data']['email'] ?? '') ?>" required>
                        <?php if (isset($_SESSION['form_errors']['email'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['email']) ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Mot de passe</label>
                        <input type="password"
                            class="form-control <?= isset($_SESSION['form_errors']['password']) ? 'is-invalid' : '' ?>"
                            id="password" name="password" required>
                        <?php if (isset($_SESSION['form_errors']['password'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['password']) ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="password_confirm" class="form-label">Confirmer le mot de passe</label>
                        <input type="password"
                            class="form-control <?= isset($_SESSION['form_errors']['password_confirm']) ? 'is-invalid' : '' ?>"
                            id="password_confirm" name="password_confirm" required>
                        <?php if (isset($_SESSION['form_errors']['password_confirm'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['password_confirm']) ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>S'inscrire
                        </button>
                    </div>
                </form>

                <div class="mt-3 text-center">
                    <p>
                        Déjà inscrit ? <a href="<?= BASE_URL ?>login">Se connecter</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
// Nettoyer les données de formulaire et les erreurs après l'affichage
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);
?>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>