<?php
echo "<h1>Test de configuration</h1>";

// Test mod_rewrite
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✓ mod_rewrite est activé</p>";
    } else {
        echo "<p style='color: red;'>✗ mod_rewrite n'est pas activé</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠ Impossible de vérifier mod_rewrite</p>";
}

// Test .htaccess
if (file_exists('.htaccess')) {
    echo "<p style='color: green;'>✓ Fichier .htaccess existe</p>";
} else {
    echo "<p style='color: red;'>✗ Fichier .htaccess manquant</p>";
}

echo "<h2>Liens de test</h2>";
echo '<p><a href="index.php?url=login">Test login (méthode sûre)</a></p>';
echo '<p><a href="login">Test login (nécessite mod_rewrite)</a></p>';
?>
