<?php
/**
 * Script de nettoyage après installation
 */

echo "<h1>Nettoyage des fichiers d'installation</h1>";

$installationFiles = [
    'install.php',
    'install_database.php',
    'check_installation.php',
    'cleanup_installation.php'
];

$deletedFiles = 0;

foreach ($installationFiles as $file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            echo "<p style='color: green;'>✓ Fichier '$file' supprimé</p>";
            $deletedFiles++;
        } else {
            echo "<p style='color: red;'>✗ Impossible de supprimer '$file'</p>";
        }
    } else {
        echo "<p style='color: gray;'>- Fichier '$file' n'existe pas</p>";
    }
}

if ($deletedFiles > 0) {
    echo "<h2 style='color: green;'>✅ Nettoyage terminé</h2>";
    echo "<p>$deletedFiles fichiers d'installation supprimés</p>";
} else {
    echo "<h2 style='color: orange;'>⚠ Aucun fichier à supprimer</h2>";
}

echo "<p><strong>Note:</strong> Ce script va également se supprimer automatiquement.</p>";
echo "<p><a href='index.php'>← Retour à l'application</a></p>";

// Auto-suppression de ce script (optionnel)
// unlink(__FILE__);
?>
