<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="container">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= BASE_URL ?>categories">Catégories</a></li>
            <li class="breadcrumb-item active" aria-current="page">Modifier la catégorie</li>
        </ol>
    </nav>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
                <i class="fas fa-edit me-2"></i>Modifier la catégorie
            </h4>
        </div>
        <div class="card-body">
            <form action="<?= BASE_URL ?>categories/update" method="post">
                <!-- Jeton CSRF et ID -->
                <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">
                <input type="hidden" name="id" value="<?= $category['id'] ?>">
                
                <div class="mb-3">
                    <label for="name" class="form-label">Nom <span class="text-danger">*</span></label>
                    <input type="text" class="form-control <?= isset($_SESSION['form_errors']['name']) ? 'is-invalid' : '' ?>" id="name" name="name" value="<?= htmlspecialchars($_SESSION['form_data']['name'] ?? $category['name']) ?>" required>
                    <?php if (isset($_SESSION['form_errors']['name'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['name']) ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control <?= isset($_SESSION['form_errors']['description']) ? 'is-invalid' : '' ?>" id="description" name="description" rows="4"><?= htmlspecialchars($_SESSION['form_data']['description'] ?? $category['description']) ?></textarea>
                    <?php if (isset($_SESSION['form_errors']['description'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['description']) ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="d-flex justify-content-between">
                    <a href="<?= BASE_URL ?>categories" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php 
// Nettoyer les données de formulaire et les erreurs après l'affichage
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);
?>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>