<?php
/**
 * Script de vérification de l'installation
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "<h1>Vérification de l'installation</h1>";

// Vérifier la connexion à la base de données
try {
    $db = Database::getInstance();
    echo "<p style='color: green;'>✓ Connexion à la base de données réussie</p>";
    
    // Vérifier les tables
    $tables = ['users', 'categories', 'archives', 'activities'];
    foreach ($tables as $table) {
        try {
            $result = $db->query("SELECT COUNT(*) as count FROM $table");
            $count = $result->fetch()['count'];
            echo "<p style='color: green;'>✓ Table '$table' existe ($count enregistrements)</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Problème avec la table '$table': " . $e->getMessage() . "</p>";
        }
    }
    
    // Vérifier l'utilisateur admin
    try {
        $admin = $db->single("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
        if ($admin) {
            echo "<p style='color: green;'>✓ Utilisateur administrateur trouvé: " . htmlspecialchars($admin['email']) . "</p>";
        } else {
            echo "<p style='color: red;'>✗ Aucun utilisateur administrateur trouvé</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Erreur lors de la vérification de l'admin: " . $e->getMessage() . "</p>";
    }
    
    // Vérifier les catégories
    try {
        $categories = $db->all("SELECT * FROM categories WHERE status = 'active'");
        echo "<p style='color: green;'>✓ " . count($categories) . " catégories actives trouvées</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Erreur lors de la vérification des catégories: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Erreur de connexion à la base de données: " . $e->getMessage() . "</p>";
}

// Vérifier les dossiers
$directories = ['uploads', 'views', 'controllers', 'models', 'includes'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        echo "<p style='color: green;'>✓ Dossier '$dir' existe</p>";
    } else {
        echo "<p style='color: red;'>✗ Dossier '$dir' manquant</p>";
    }
}

// Vérifier les permissions
if (is_writable('uploads')) {
    echo "<p style='color: green;'>✓ Dossier 'uploads' est accessible en écriture</p>";
} else {
    echo "<p style='color: orange;'>⚠ Dossier 'uploads' n'est pas accessible en écriture</p>";
}

// Vérifier les fichiers de configuration
$configFiles = ['config/config.php', 'config/database.php', '.htaccess'];
foreach ($configFiles as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ Fichier '$file' existe</p>";
    } else {
        echo "<p style='color: red;'>✗ Fichier '$file' manquant</p>";
    }
}

echo "<h2>Test des routes principales</h2>";
$routes = [
    '' => 'Page d\'accueil',
    'login' => 'Page de connexion',
    'archives' => 'Liste des archives',
    'categories' => 'Gestion des catégories'
];

foreach ($routes as $route => $description) {
    $url = 'index.php' . ($route ? '?url=' . $route : '');
    echo "<p><a href='$url' target='_blank'>$description</a> - <code>$url</code></p>";
}

echo "<h2>Informations de connexion</h2>";
echo "<p><strong>Email administrateur:</strong> <EMAIL></p>";
echo "<p><strong>Mot de passe:</strong> password</p>";

echo "<hr>";
echo "<p><a href='index.php'>← Retour à l'application</a></p>";
?>
