<?php
require_once dirname(__DIR__) . '/config/database.php';

/**
 * Modèle pour la gestion des utilisateurs
 */
class UserModel
{
    private $db;
    private $table = 'users';

    /**
     * Constructeur
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Récupère tous les utilisateurs
     * @param array $options Options de filtrage et de pagination
     * @return array Les utilisateurs
     */
    public function getAll($options = [])
    {
        // Construire la requête SQL
        $sql = "SELECT * FROM {$this->table}";

        // Filtrage par rôle
        if (isset($options['role']) && !empty($options['role'])) {
            $sql .= " WHERE role = :role";
        }

        // Tri
        $sql .= " ORDER BY " . ($options['order_by'] ?? 'name') . " " . ($options['order_dir'] ?? 'ASC');

        // Pagination
        if (isset($options['limit']) && isset($options['offset'])) {
            $sql .= " LIMIT :offset, :limit";
        }

        // Préparer les paramètres
        $params = [];

        if (isset($options['role']) && !empty($options['role'])) {
            $params['role'] = $options['role'];
        }

        if (isset($options['limit']) && isset($options['offset'])) {
            $params['limit'] = $options['limit'];
            $params['offset'] = $options['offset'];
        }

        // Exécuter la requête
        return $this->db->all($sql, $params);
    }

    /**
     * Compte le nombre total d'utilisateurs
     * @param array $options Options de filtrage
     * @return int Le nombre d'utilisateurs
     */
    public function count($options = [])
    {
        // Construire la requête SQL
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";

        // Filtrage par rôle
        if (isset($options['role']) && !empty($options['role'])) {
            $sql .= " WHERE role = :role";
        }

        // Préparer les paramètres
        $params = [];

        if (isset($options['role']) && !empty($options['role'])) {
            $params['role'] = $options['role'];
        }

        // Exécuter la requête
        $result = $this->db->single($sql, $params);

        return $result['count'];
    }

    /**
     * Récupère un utilisateur par son ID
     * @param int $id L'ID de l'utilisateur
     * @return array|false L'utilisateur ou false si non trouvé
     */
    public function findById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = :id";
        return $this->db->single($sql, ['id' => $id]);
    }

    /**
     * Récupère un utilisateur par son email
     * @param string $email L'email de l'utilisateur
     * @return array|false L'utilisateur ou false si non trouvé
     */
    public function findByEmail($email)
    {
        $sql = "SELECT * FROM {$this->table} WHERE email = :email";
        return $this->db->single($sql, ['email' => $email]);
    }

    /**
     * Vérifie si un email existe déjà
     * @param string $email L'email à vérifier
     * @param int $excludeId ID de l'utilisateur à exclure (pour les mises à jour)
     * @return bool True si l'email existe, false sinon
     */
    public function emailExists($email, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = :email";
        $params = ['email' => $email];

        if ($excludeId !== null) {
            $sql .= " AND id != :id";
            $params['id'] = $excludeId;
        }

        $result = $this->db->single($sql, $params);

        return $result['count'] > 0;
    }

    /**
     * Crée un nouvel utilisateur
     * @param array $data Les données de l'utilisateur
     * @return int|false L'ID de l'utilisateur créé ou false en cas d'erreur
     */
    public function create($data)
    {
        $sql = "INSERT INTO {$this->table} (name, email, password, role, status, created_at) 
                VALUES (:name, :email, :password, :role, :status, NOW())";

        $params = [
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => $data['password'],
            'role' => $data['role'] ?? 'user',
            'status' => $data['status'] ?? 'active'
        ];

        $this->db->query($sql, $params);

        return $this->db->lastInsertId();
    }

    /**
     * Met à jour un utilisateur
     * @param int $id L'ID de l'utilisateur
     * @param array $data Les données à mettre à jour
     * @return bool True si la mise à jour a réussi, false sinon
     */
    public function update($id, $data)
    {
        // Construire la requête SQL
        $sql = "UPDATE {$this->table} SET ";
        $updates = [];
        $params = ['id' => $id];

        // Ajouter les champs à mettre à jour
        if (isset($data['name'])) {
            $updates[] = "name = :name";
            $params['name'] = $data['name'];
        }

        if (isset($data['email'])) {
            $updates[] = "email = :email";
            $params['email'] = $data['email'];
        }

        if (isset($data['password'])) {
            $updates[] = "password = :password";
            $params['password'] = $data['password'];
        }

        if (isset($data['role'])) {
            $updates[] = "role = :role";
            $params['role'] = $data['role'];
        }

        if (isset($data['status'])) {
            $updates[] = "status = :status";
            $params['status'] = $data['status'];
        }

        $updates[] = "updated_at = NOW()";

        $sql .= implode(", ", $updates);
        $sql .= " WHERE id = :id";

        // Exécuter la requête
        $this->db->query($sql, $params);

        return true;
    }

    /**
     * Met à jour le mot de passe d'un utilisateur
     * @param int $id L'ID de l'utilisateur
     * @param string $password Le nouveau mot de passe (déjà haché)
     * @return bool True si la mise à jour a réussi, false sinon
     */
    public function updatePassword($id, $password)
    {
        $sql = "UPDATE {$this->table} SET password = :password, updated_at = NOW() WHERE id = :id";
        $this->db->query($sql, ['id' => $id, 'password' => $password]);
        return true;
    }

    /**
     * Met à jour la date de dernière connexion
     * @param int $id L'ID de l'utilisateur
     * @return bool True si la mise à jour a réussi, false sinon
     */
    public function updateLastLogin($id)
    {
        $sql = "UPDATE {$this->table} SET last_login = NOW() WHERE id = :id";
        $this->db->query($sql, ['id' => $id]);
        return true;
    }

    /**
     * Supprime un utilisateur
     * @param int $id L'ID de l'utilisateur
     * @return bool True si la suppression a réussi, false sinon
     */
    public function delete($id)
    {
        $sql = "DELETE FROM {$this->table} WHERE id = :id";
        $this->db->query($sql, ['id' => $id]);
        return true;
    }

    /**
     * Enregistre un token de rappel ("Se souvenir de moi")
     * @param int $userId L'ID de l'utilisateur
     * @param string $token Le token
     * @param int $expiry La date d'expiration (timestamp)
     * @return bool True si l'enregistrement a réussi, false sinon
     */
    public function saveRememberToken($userId, $token, $expiry)
    {
        $sql = "INSERT INTO user_tokens (user_id, token, type, expires_at, created_at) 
                VALUES (:user_id, :token, 'remember', FROM_UNIXTIME(:expiry), NOW())";

        $params = [
            'user_id' => $userId,
            'token' => $token,
            'expiry' => $expiry
        ];

        $this->db->query($sql, $params);

        return true;
    }

    /**
     * Supprime un token de rappel
     * @param string $token Le token à supprimer
     * @return bool True si la suppression a réussi, false sinon
     */
    public function deleteRememberToken($token)
    {
        $sql = "DELETE FROM user_tokens WHERE token = :token AND type = 'remember'";
        $this->db->query($sql, ['token' => $token]);
        return true;
    }

    /**
     * Récupère un utilisateur par son token de rappel
     * @param string $token Le token
     * @return array|false L'utilisateur ou false si non trouvé ou token expiré
     */
    public function findByRememberToken($token)
    {
        $sql = "SELECT u.* FROM {$this->table} u 
                JOIN user_tokens t ON u.id = t.user_id 
                WHERE t.token = :token AND t.type = 'remember' AND t.expires_at > NOW()";

        return $this->db->single($sql, ['token' => $token]);
    }

    /**
     * Enregistre un token de réinitialisation de mot de passe
     * @param int $userId L'ID de l'utilisateur
     * @param string $token Le token
     * @param int $expiry La date d'expiration (timestamp)
     * @return bool True si l'enregistrement a réussi, false sinon
     */
    public function saveResetToken($userId, $token, $expiry)
    {
        $sql = "INSERT INTO user_tokens (user_id, token, type, expires_at, created_at) 
                VALUES (:user_id, :token, 'reset', FROM_UNIXTIME(:expiry), NOW())";

        $params = [
            'user_id' => $userId,
            'token' => $token,
            'expiry' => $expiry
        ];

        $this->db->query($sql, $params);

        return true;
    }

    /**
     * Supprime un token de réinitialisation de mot de passe
     * @param string $token Le token à supprimer
     * @return bool True si la suppression a réussi, false sinon
     */
    public function deleteResetToken($token)
    {
        $sql = "DELETE FROM user_tokens WHERE token = :token AND type = 'reset'";
        $this->db->query($sql, ['token' => $token]);
        return true;
    }

    /**
     * Récupère un utilisateur par son token de réinitialisation
     * @param string $token Le token
     * @return array|false L'utilisateur ou false si non trouvé ou token expiré
     */
    public function findByResetToken($token)
    {
        $sql = "SELECT u.* FROM {$this->table} u 
                JOIN user_tokens t ON u.id = t.user_id 
                WHERE t.token = :token AND t.type = 'reset' AND t.expires_at > NOW()";

        return $this->db->single($sql, ['token' => $token]);
    }
}
