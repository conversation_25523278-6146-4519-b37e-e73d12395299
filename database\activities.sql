-- Table pour stocker les activités des utilisateurs
CREATE TABLE IF NOT EXISTS `activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action_type` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `entity_type` varchar(50) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `action_type` (`action_type`),
  KEY `entity_type` (`entity_type`),
  KEY `entity_id` (`entity_id`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `fk_activities_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Ajouter des exemples d'activités
INSERT INTO `activities` (`user_id`, `action_type`, `description`, `entity_type`, `entity_id`, `ip_address`, `created_at`) VALUES
(1, 'login', 'Connexion au système', NULL, NULL, '127.0.0.1', NOW() - INTERVAL 7 DAY),
(1, 'create', 'Création d\'une nouvelle archive', 'archive', 1, '127.0.0.1', NOW() - INTERVAL 6 DAY),
(2, 'login', 'Connexion au système', NULL, NULL, '127.0.0.1', NOW() - INTERVAL 6 DAY),
(2, 'view', 'Consultation d\'une archive', 'archive', 1, '127.0.0.1', NOW() - INTERVAL 6 DAY),
(1, 'update', 'Mise à jour d\'une archive', 'archive', 1, '127.0.0.1', NOW() - INTERVAL 5 DAY),
(1, 'create', 'Création d\'une nouvelle catégorie', 'category', 1, '127.0.0.1', NOW() - INTERVAL 5 DAY),
(2, 'download', 'Téléchargement d\'une archive', 'archive', 1, '127.0.0.1', NOW() - INTERVAL 4 DAY),
(1, 'create', 'Création d\'un nouvel utilisateur', 'user', 3, '127.0.0.1', NOW() - INTERVAL 3 DAY),
(3, 'login', 'Connexion au système', NULL, NULL, '127.0.0.1', NOW() - INTERVAL 2 DAY),
(3, 'create', 'Création d\'une nouvelle archive', 'archive', 2, '127.0.0.1', NOW() - INTERVAL 2 DAY),
(1, 'update', 'Mise à jour d\'une catégorie', 'category', 1, '127.0.0.1', NOW() - INTERVAL 1 DAY),
(2, 'logout', 'Déconnexion du système', NULL, NULL, '127.0.0.1', NOW() - INTERVAL 1 DAY),
(1, 'delete', 'Suppression d\'une archive', 'archive', 3, '127.0.0.1', NOW());