<?php
require_once CONTROLLERS_PATH . 'BaseController.php';
require_once MODELS_PATH . 'ArchiveModel.php';
require_once MODELS_PATH . 'CategoryModel.php';
require_once MODELS_PATH . 'UserModel.php';
require_once MODELS_PATH . 'ActivityModel.php';

/**
 * Contrôleur pour la gestion des rapports
 */
class ReportController extends BaseController {
    private $archiveModel;
    private $categoryModel;
    private $userModel;
    private $activityModel;
    
    /**
     * Constructeur
     */
    public function __construct() {
        parent::__construct();
        $this->archiveModel = new ArchiveModel();
        $this->categoryModel = new CategoryModel();
        $this->userModel = new UserModel();
        $this->activityModel = new ActivityModel();
    }
    
    /**
     * Affiche la page d'accueil des rapports
     */
    public function index() {
        // Vérifier si l'utilisateur est connecté et est administrateur
        $this->requireAdmin();
        
        // Charger la vue
        $this->loadView('reports/index');
    }
    
    /**
     * Génère un rapport sur les archives
     */
    public function archives() {
        // Vérifier si l'utilisateur est connecté et est administrateur
        $this->requireAdmin();
        
        // Paramètres du rapport
        $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-1 month'));
        $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
        $category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
        $status = isset($_GET['status']) ? $_GET['status'] : '';
        $format = isset($_GET['format']) ? $_GET['format'] : 'html';
        
        // Options de filtrage
        $options = [
            'date_from' => $start_date,
            'date_to' => $end_date,
            'category_id' => $category_id,
            'status' => $status,
            'order_by' => 'created_at',
            'order_dir' => 'DESC'
        ];
        
        // Récupérer les archives
        $archives = $this->archiveModel->getAll($options);
        $totalArchives = $this->archiveModel->count($options);
        
        // Récupérer les catégories pour le filtre
        $categories = $this->categoryModel->getForDropdown(true);
        
        // Statistiques
        $stats = [
            'total' => $totalArchives,
            'by_status' => $this->archiveModel->countByStatus($options),
            'by_category' => $this->archiveModel->countByCategory($options)
        ];
        
        // Générer le rapport selon le format demandé
        switch ($format) {
            case 'csv':
                $this->generateCsvReport($archives, $stats, $start_date, $end_date);
                break;
            
            case 'pdf':
                $this->generatePdfReport($archives, $stats, $start_date, $end_date, $categories);
                break;
            
            case 'html':
            default:
                // Charger la vue
                $this->loadView('reports/archives', [
                    'archives' => $archives,
                    'categories' => $categories,
                    'stats' => $stats,
                    'options' => $options
                ]);
                break;
        }
    }
    
    /**
     * Génère un rapport sur les utilisateurs
     */
    public function users() {
        // Vérifier si l'utilisateur est connecté et est administrateur
        $this->requireAdmin();
        
        // Paramètres du rapport
        $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-1 month'));
        $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
        $role = isset($_GET['role']) ? $_GET['role'] : '';
        $status = isset($_GET['status']) ? $_GET['status'] : '';
        $format = isset($_GET['format']) ? $_GET['format'] : 'html';
        
        // Options de filtrage
        $options = [
            'date_from' => $start_date,
            'date_to' => $end_date,
            'role' => $role,
            'status' => $status,
            'order_by' => 'created_at',
            'order_dir' => 'DESC'
        ];
        
        // Récupérer les utilisateurs
        $users = $this->userModel->getAll($options);
        $totalUsers = $this->userModel->count($options);
        
        // Statistiques
        $stats = [
            'total' => $totalUsers,
            'by_role' => [
                'admin' => $this->userModel->count(['role' => 'admin']),
                'user' => $this->userModel->count(['role' => 'user'])
            ],
            'by_status' => [
                'active' => $this->userModel->count(['status' => 'active']),
                'inactive' => $this->userModel->count(['status' => 'inactive'])
            ]
        ];
        
        // Générer le rapport selon le format demandé
        switch ($format) {
            case 'csv':
                $this->generateUsersCsvReport($users, $stats, $start_date, $end_date);
                break;
            
            case 'pdf':
                $this->generateUsersPdfReport($users, $stats, $start_date, $end_date);
                break;
            
            case 'html':
            default:
                // Charger la vue
                $this->loadView('reports/users', [
                    'users' => $users,
                    'stats' => $stats,
                    'options' => $options
                ]);
                break;
        }
    }
    
    /**
     * Génère un rapport sur les activités
     */
    public function activities() {
        // Vérifier si l'utilisateur est connecté et est administrateur
        $this->requireAdmin();
        
        // Paramètres du rapport
        $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-1 week'));
        $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
        $user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;
        $action = isset($_GET['action']) ? $_GET['action'] : '';
        $format = isset($_GET['format']) ? $_GET['format'] : 'html';
        
        // Options de filtrage
        $options = [
            'date_from' => $start_date,
            'date_to' => $end_date,
            'user_id' => $user_id,
            'action' => $action,
            'order_by' => 'created_at',
            'order_dir' => 'DESC'
        ];
        
        // Récupérer les activités
        $activities = $this->activityModel->getAll($options);
        $totalActivities = $this->activityModel->countAll($options);
        
        // Récupérer les statistiques
        $stats = [
            'by_action_type' => $this->activityModel->getStatsByActionType($options),
            'by_day' => $this->activityModel->getStatsByDay($options),
            'most_active_users' => $this->activityModel->getMostActiveUsers($options, 5)
        ];
        
        // Récupérer les utilisateurs pour le filtre
        $users = $this->userModel->getForDropdown();
        
        // Générer le rapport selon le format demandé
        switch ($format) {
            case 'csv':
                $this->generateActivitiesCsvReport($activities, $stats, $start_date, $end_date);
                break;
            
            case 'pdf':
                $this->generateActivitiesPdfReport($activities, $stats, $start_date, $end_date);
                break;
            
            case 'html':
            default:
                // Charger la vue
                $this->loadView('reports/activities', [
                    'activities' => $activities,
                    'users' => $users,
                    'totalActivities' => $totalActivities,
                    'stats' => $stats,
                    'options' => $options
                ]);
                break;
        }
    }
    }
    
    /**
     * Génère un rapport CSV pour les archives
     * @param array $archives Les archives
     * @param array $stats Les statistiques
     * @param string $start_date Date de début
     * @param string $end_date Date de fin
     */
    private function generateCsvReport($archives, $stats, $start_date, $end_date) {
    {
        // Définir les en-têtes HTTP pour le téléchargement du fichier CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="rapport_archives_' . date('Y-m-d') . '.csv"');
        
        // Créer le flux de sortie
        $output = fopen('php://output', 'w');
        
        // Ajouter l'en-tête UTF-8 BOM pour Excel
        fprintf($output, "\xEF\xBB\xBF");
        
        // Écrire l'en-tête du rapport
        fputcsv($output, ['Rapport des archives', 'Période: ' . $start_date . ' au ' . $end_date]);
        fputcsv($output, ['Total des archives: ' . $stats['total']]);
        fputcsv($output, []);
        
        // Écrire les en-têtes des colonnes
        fputcsv($output, ['Référence', 'Titre', 'Catégorie', 'Statut', 'Date de création', 'Créé par', 'Taille du fichier']);
        
        // Écrire les données des archives
        foreach ($archives as $archive) {
            fputcsv($output, [
                $archive['reference'],
                $archive['title'],
                $archive['category_name'],
                $archive['status'],
                $archive['created_at'],
                $archive['created_by_name'],
                formatFileSize($archive['file_size'])
            ]);
        }
        
        // Fermer le flux
        fclose($output);
        exit;
    }
    
    /**
     * Génère un rapport PDF pour les archives
     * @param array $archives Les archives
     * @param array $stats Les statistiques
     * @param string $start_date Date de début
     * @param string $end_date Date de fin
     * @param array $categories Les catégories
     */
    protected function generatePdfReport($archives, $stats, $start_date, $end_date, $categories) {
        // Note: Cette fonction nécessite une bibliothèque de génération de PDF comme FPDF ou TCPDF
        // Pour l'instant, nous redirigeons vers la vue HTML
        setFlashMessage('info', 'La génération de PDF n\'est pas encore implémentée. Voici la version HTML du rapport.');
        
        // Charger la vue
        $this->loadView('reports/archives', [
            'archives' => $archives,
            'categories' => $categories,
            'stats' => $stats,
            'options' => [
                'date_from' => $start_date,
                'date_to' => $end_date
            ]
        ]);
    }
    
    /**
     * Génère un rapport CSV pour les utilisateurs
     * @param array $users Les utilisateurs
     * @param array $stats Les statistiques
     * @param string $start_date Date de début
     * @param string $end_date Date de fin
     */
    protected function generateUsersCsvReport($users, $stats, $start_date, $end_date) {
        // Définir les en-têtes HTTP pour le téléchargement du fichier CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="rapport_utilisateurs_' . date('Y-m-d') . '.csv"');
        
        // Créer le flux de sortie
        $output = fopen('php://output', 'w');
        
        // Ajouter l'en-tête UTF-8 BOM pour Excel
        fprintf($output, "\xEF\xBB\xBF");
        
        // Écrire l'en-tête du rapport
        fputcsv($output, ['Rapport des utilisateurs', 'Période: ' . $start_date . ' au ' . $end_date]);
        fputcsv($output, ['Total des utilisateurs: ' . $stats['total']]);
        fputcsv($output, []);
        
        // Écrire les en-têtes des colonnes
        fputcsv($output, ['ID', 'Nom', 'Email', 'Rôle', 'Statut', 'Date de création', 'Dernière connexion']);
        
        // Écrire les données des utilisateurs
        foreach ($users as $user) {
            fputcsv($output, [
                $user['id'],
                $user['name'],
                $user['email'],
                $user['role'],
                $user['status'],
                $user['created_at'],
                $user['last_login'] ?? 'Jamais'
            ]);
        }
        
        // Fermer le flux
        fclose($output);
        exit;
    }
    
    /**
     * Génère un rapport PDF pour les utilisateurs
     * @param array $users Les utilisateurs
     * @param array $stats Les statistiques
     * @param string $start_date Date de début
     * @param string $end_date Date de fin
     */
    private function generateUsersPdfReport($users, $stats, $start_date, $end_date) {
        // Note: Cette fonction nécessite une bibliothèque de génération de PDF comme FPDF ou TCPDF
        // Pour l'instant, nous redirigeons vers la vue HTML
        setFlashMessage('info', 'La génération de PDF n\'est pas encore implémentée. Voici la version HTML du rapport.');
        
        // Charger la vue
        $this->loadView('reports/users', [
            'users' => $users,
            'stats' => $stats,
            'options' => [
                'date_from' => $start_date,
                'date_to' => $end_date
            ]
        ]);
    }
    
    /**
     * Génère un rapport CSV pour les activités
     * @param array $activities Les activités
     * @param array $stats Les statistiques
     * @param string $start_date Date de début
     * @param string $end_date Date de fin
     */
    private function generateActivitiesCsvReport($activities, $stats, $start_date, $end_date) {
        // Définir les en-têtes HTTP pour le téléchargement du fichier CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="rapport_activites_' . date('Y-m-d') . '.csv"');
        
        // Créer le flux de sortie
        $output = fopen('php://output', 'w');
        
        // Ajouter l'en-tête UTF-8 BOM pour Excel
        fprintf($output, "\xEF\xBB\xBF");
        
        // Écrire l'en-tête du rapport
        fputcsv($output, ['Rapport des activités', 'Période: ' . $start_date . ' au ' . $end_date]);
        fputcsv($output, ['Total des activités: ' . count($activities)]);
        fputcsv($output, []);
        
        // Écrire les en-têtes des colonnes
        fputcsv($output, ['Date et heure', 'Utilisateur', 'Action', 'Description', 'Adresse IP']);
        
        // Écrire les données des activités
        foreach ($activities as $activity) {
            fputcsv($output, [
                $activity['created_at'],
                $activity['username'] ?? 'Système',
                $activity['action_type'],
                $activity['description'],
                $activity['ip_address']
            ]);
        }
        
        // Fermer le flux
        fclose($output);
        exit;
    }
    
    /**
     * Génère un rapport PDF pour les activités
     * @param array $activities Les activités
     * @param array $stats Les statistiques
     * @param string $start_date Date de début
     * @param string $end_date Date de fin
     */
    private function generateActivitiesPdfReport($activities, $stats, $start_date, $end_date) {
        // Note: Cette fonction nécessite une bibliothèque de génération de PDF comme FPDF ou TCPDF
        // Pour l'instant, nous redirigeons vers la vue HTML
        setFlashMessage('info', 'La génération de PDF n\'est pas encore implémentée. Voici la version HTML du rapport.');
        
        // Récupérer les utilisateurs pour le filtre
        $users = $this->userModel->getForDropdown();
        
        // Charger la vue
        $this->loadView('reports/activities', [
            'activities' => $activities,
            'users' => $users,
            'stats' => $stats,
            'options' => [
                'date_from' => $start_date,
                'date_to' => $end_date
            ]
        ]);
    }
}
?>