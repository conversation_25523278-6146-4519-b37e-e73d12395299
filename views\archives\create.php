<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="container">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= BASE_URL ?>archives">Archives</a></li>
            <li class="breadcrumb-item active" aria-current="page">Nouvelle archive</li>
        </ol>
    </nav>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
                <i class="fas fa-plus-circle me-2"></i>Ajouter une nouvelle archive
            </h4>
        </div>
        <div class="card-body">
            <form action="<?= BASE_URL ?>archives/store" method="post" enctype="multipart/form-data">
                <!-- Jeton CSRF -->
                <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2">Informations générales</h5>
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">Titre <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?= isset($_SESSION['form_errors']['title']) ? 'is-invalid' : '' ?>" id="title" name="title" value="<?= htmlspecialchars($_SESSION['form_data']['title'] ?? '') ?>" required>
                            <?php if (isset($_SESSION['form_errors']['title'])): ?>
                                <div class="invalid-feedback">
                                    <?= htmlspecialchars($_SESSION['form_errors']['title']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category_id" class="form-label">Catégorie <span class="text-danger">*</span></label>
                            <select class="form-select <?= isset($_SESSION['form_errors']['category_id']) ? 'is-invalid' : '' ?>" id="category_id" name="category_id" required>
                                <option value="">Sélectionner une catégorie</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>" <?= (isset($_SESSION['form_data']['category_id']) && $_SESSION['form_data']['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (isset($_SESSION['form_errors']['category_id'])): ?>
                                <div class="invalid-feedback">
                                    <?= htmlspecialchars($_SESSION['form_errors']['category_id']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="mb-3">
                            <label for="status" class="form-label">Statut <span class="text-danger">*</span></label>
                            <select class="form-select <?= isset($_SESSION['form_errors']['status']) ? 'is-invalid' : '' ?>" id="status" name="status" required>
                                <option value="active" <?= (isset($_SESSION['form_data']['status']) && $_SESSION['form_data']['status'] == 'active') ? 'selected' : '' ?>>Actif</option>
                                <option value="inactive" <?= (isset($_SESSION['form_data']['status']) && $_SESSION['form_data']['status'] == 'inactive') ? 'selected' : '' ?>>Inactif</option>
                                <option value="archived" <?= (isset($_SESSION['form_data']['status']) && $_SESSION['form_data']['status'] == 'archived') ? 'selected' : '' ?>>Archivé</option>
                            </select>
                            <?php if (isset($_SESSION['form_errors']['status'])): ?>
                                <div class="invalid-feedback">
                                    <?= htmlspecialchars($_SESSION['form_errors']['status']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control <?= isset($_SESSION['form_errors']['description']) ? 'is-invalid' : '' ?>" id="description" name="description" rows="5"><?= htmlspecialchars($_SESSION['form_data']['description'] ?? '') ?></textarea>
                            <?php if (isset($_SESSION['form_errors']['description'])): ?>
                                <div class="invalid-feedback">
                                    <?= htmlspecialchars($_SESSION['form_errors']['description']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2">Fichier et métadonnées</h5>
                        
                        <div class="mb-3">
                            <label for="file" class="form-label">Fichier <span class="text-danger">*</span></label>
                            <input type="file" class="form-control <?= isset($_SESSION['form_errors']['file']) ? 'is-invalid' : '' ?>" id="file" name="file" required>
                            <div class="form-text">Taille maximale: <?= MAX_FILE_SIZE / 1024 / 1024 ?>MB. Extensions autorisées: <?= implode(', ', ALLOWED_EXTENSIONS) ?></div>
                            <?php if (isset($_SESSION['form_errors']['file'])): ?>
                                <div class="invalid-feedback">
                                    <?= htmlspecialchars($_SESSION['form_errors']['file']) ?>
                                </div>
                            <?php endif; ?>
                            <div id="preview-container" class="mt-2"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Métadonnées (optionnel)</label>
                            <div class="card">
                                <div class="card-body p-3">
                                    <div id="metadata-container">
                                        <div class="row mb-2 metadata-row">
                                            <div class="col-5">
                                                <input type="text" class="form-control" name="meta_keys[]" placeholder="Clé">
                                            </div>
                                            <div class="col-5">
                                                <input type="text" class="form-control" name="meta_values[]" placeholder="Valeur">
                                            </div>
                                            <div class="col-2">
                                                <button type="button" class="btn btn-danger w-100 remove-metadata" disabled>
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-secondary mt-2" id="add-metadata">
                                        <i class="fas fa-plus me-2"></i>Ajouter une métadonnée
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between">
                    <a href="<?= BASE_URL ?>archives" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion des métadonnées
        const metadataContainer = document.getElementById('metadata-container');
        const addMetadataBtn = document.getElementById('add-metadata');
        
        // Ajouter une nouvelle ligne de métadonnées
        addMetadataBtn.addEventListener('click', function() {
            const newRow = document.createElement('div');
            newRow.className = 'row mb-2 metadata-row';
            newRow.innerHTML = `
                <div class="col-5">
                    <input type="text" class="form-control" name="meta_keys[]" placeholder="Clé">
                </div>
                <div class="col-5">
                    <input type="text" class="form-control" name="meta_values[]" placeholder="Valeur">
                </div>
                <div class="col-2">
                    <button type="button" class="btn btn-danger w-100 remove-metadata">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            metadataContainer.appendChild(newRow);
            
            // Activer le bouton de suppression pour la première ligne
            if (metadataContainer.querySelectorAll('.metadata-row').length > 1) {
                metadataContainer.querySelector('.remove-metadata[disabled]').removeAttribute('disabled');
            }
            
            // Ajouter l'événement de suppression
            newRow.querySelector('.remove-metadata').addEventListener('click', function() {
                newRow.remove();
                // Désactiver le bouton de suppression pour la première ligne si c'est la seule
                if (metadataContainer.querySelectorAll('.metadata-row').length === 1) {
                    metadataContainer.querySelector('.remove-metadata').setAttribute('disabled', 'disabled');
                }
            });
        });
    });
</script>

<?php 
// Nettoyer les données de formulaire et les erreurs après l'affichage
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);
?>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>