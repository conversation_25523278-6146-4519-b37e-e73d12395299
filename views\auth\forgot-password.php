<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Mot de passe oublié</h4>
            </div>
            <div class="card-body">
                <p class="mb-4">Entrez votre adresse email pour recevoir un lien de réinitialisation de mot de passe.
                </p>

                <form action="<?= BASE_URL ?>send-reset-link" method="post">
                    <!-- Jeton CSRF -->
                    <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email"
                            class="form-control <?= isset($_SESSION['form_errors']['email']) ? 'is-invalid' : '' ?>"
                            id="email" name="email"
                            value="<?= htmlspecialchars($_SESSION['form_data']['email'] ?? '') ?>" required>
                        <?php if (isset($_SESSION['form_errors']['email'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['email']) ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Envoyer le lien de réinitialisation
                        </button>
                    </div>
                </form>

                <div class="mt-3 text-center">
                    <p>
                        <a href="<?= BASE_URL ?>login">Retour à la connexion</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
// Nettoyer les données de formulaire et les erreurs après l'affichage
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);
?>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>