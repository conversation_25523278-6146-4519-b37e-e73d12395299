<?php
/**
 * Script d'installation de la base de données
 */

// Configuration de la base de données
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'archive_db';

echo "<h1>Installation de la base de données Archive</h1>";

try {
    // Connexion à MySQL sans spécifier de base de données
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✓ Connexion à MySQL réussie</p>";
    
    // Créer la base de données
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✓ Base de données '$database' créée</p>";
    
    // Se connecter à la base de données
    $pdo->exec("USE `$database`");
    echo "<p>✓ Connexion à la base de données '$database'</p>";
    
    // Lire et exécuter le script SQL
    $sqlFile = 'database/create_database.sql';
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        
        // Diviser le script en requêtes individuelles
        $queries = explode(';', $sql);
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^(--|CREATE DATABASE|USE)/i', $query)) {
                try {
                    $pdo->exec($query);
                } catch (PDOException $e) {
                    // Ignorer les erreurs de tables déjà existantes
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "<p style='color: orange;'>⚠ Avertissement: " . $e->getMessage() . "</p>";
                    }
                }
            }
        }
        
        echo "<p>✓ Tables créées avec succès</p>";
    } else {
        echo "<p style='color: red;'>✗ Fichier SQL non trouvé: $sqlFile</p>";
    }
    
    // Créer l'utilisateur administrateur par défaut
    $adminEmail = '<EMAIL>';
    $adminPassword = password_hash('password', PASSWORD_DEFAULT);
    
    // Vérifier si l'admin existe déjà
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$adminEmail]);
    
    if (!$stmt->fetch()) {
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, 'admin', 'active')");
        $stmt->execute(['Administrateur', $adminEmail, $adminPassword]);
        echo "<p>✓ Utilisateur administrateur créé</p>";
        echo "<p><strong>Email:</strong> $adminEmail</p>";
        echo "<p><strong>Mot de passe:</strong> password</p>";
    } else {
        echo "<p>✓ Utilisateur administrateur déjà existant</p>";
    }
    
    // Créer les catégories par défaut
    $categories = [
        ['Documents administratifs', 'Documents relatifs à l\'administration'],
        ['Contrats', 'Contrats et accords'],
        ['Factures', 'Factures et documents comptables'],
        ['Correspondance', 'Lettres et emails'],
        ['Rapports', 'Rapports et analyses']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO categories (name, description, status) VALUES (?, ?, 'active')");
    $categoriesCreated = 0;
    
    foreach ($categories as $category) {
        if ($stmt->execute($category)) {
            $categoriesCreated++;
        }
    }
    
    if ($categoriesCreated > 0) {
        echo "<p>✓ $categoriesCreated catégories créées</p>";
    } else {
        echo "<p>✓ Catégories déjà existantes</p>";
    }
    
    // Créer le dossier uploads s'il n'existe pas
    $uploadsDir = 'uploads';
    if (!is_dir($uploadsDir)) {
        if (mkdir($uploadsDir, 0755, true)) {
            echo "<p>✓ Dossier uploads créé</p>";
        } else {
            echo "<p style='color: red;'>✗ Impossible de créer le dossier uploads</p>";
        }
    } else {
        echo "<p>✓ Dossier uploads existe</p>";
    }
    
    // Créer le fichier .htaccess pour protéger le dossier uploads
    $htaccessContent = "Options -Indexes\nDeny from all\n<Files ~ \"\\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|jpg|jpeg|png|gif)$\">\n    Allow from all\n</Files>";
    file_put_contents($uploadsDir . '/.htaccess', $htaccessContent);
    echo "<p>✓ Protection du dossier uploads configurée</p>";
    
    echo "<h2 style='color: green;'>✅ Installation terminée avec succès!</h2>";
    echo "<p><a href='index.php'>Accéder à l'application</a></p>";
    echo "<p><a href='index.php?url=login'>Se connecter</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Erreur: " . $e->getMessage() . "</p>";
    echo "<h3>Vérifications à effectuer:</h3>";
    echo "<ul>";
    echo "<li>MySQL est-il démarré dans Laragon?</li>";
    echo "<li>Les paramètres de connexion sont-ils corrects?</li>";
    echo "<li>L'utilisateur MySQL a-t-il les permissions nécessaires?</li>";
    echo "</ul>";
}
?>
