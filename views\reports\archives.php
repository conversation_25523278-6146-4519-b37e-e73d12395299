<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= BASE_URL ?>">Accueil</a></li>
                    <li class="breadcrumb-item"><a href="<?= BASE_URL ?>reports">Rapports</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Archives</li>
                </ol>
            </nav>
            
            <h1 class="mb-4"><i class="fas fa-file-alt me-2"></i>Rapport des Archives</h1>
            
            <!-- Filtres -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0"><i class="fas fa-filter me-2"></i>Filtres</h3>
                </div>
                <div class="card-body">
                    <form action="<?= BASE_URL ?>reports/archives" method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Date de début</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="<?= htmlspecialchars($options['date_from'] ?? date('Y-m-d', strtotime('-1 month'))) ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">Date de fin</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="<?= htmlspecialchars($options['date_to'] ?? date('Y-m-d')) ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="category_id" class="form-label">Catégorie</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="0">Toutes les catégories</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>" <?= ($options['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="status" class="form-label">Statut</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Tous les statuts</option>
                                <option value="active" <?= ($options['status'] == 'active') ? 'selected' : '' ?>>Actif</option>
                                <option value="inactive" <?= ($options['status'] == 'inactive') ? 'selected' : '' ?>>Inactif</option>
                                <option value="archived" <?= ($options['status'] == 'archived') ? 'selected' : '' ?>>Archivé</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Format d'exportation</label>
                            <div class="d-flex">
                                <a href="<?= BASE_URL ?>reports/archives?format=csv<?= http_build_query(array_filter($options)) ?>" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-file-csv me-2"></i>Exporter en CSV
                                </a>
                                <a href="<?= BASE_URL ?>reports/archives?format=pdf<?= http_build_query(array_filter($options)) ?>" class="btn btn-outline-danger">
                                    <i class="fas fa-file-pdf me-2"></i>Exporter en PDF
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6 d-flex align-items-end justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Appliquer les filtres
                            </button>
                            <a href="<?= BASE_URL ?>reports/archives" class="btn btn-secondary ms-2">
                                <i class="fas fa-undo me-2"></i>Réinitialiser
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Statistiques générales -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white h-100">
                        <div class="card-body text-center">
                            <h3 class="display-4"><?= number_format($stats['total'], 0, ',', ' ') ?></h3>
                            <p class="card-text">Archives au total</p>
                        </div>
                    </div>
                </div>
                
                <?php foreach ($stats['by_status'] as $status => $count): ?>
                    <div class="col-md-3 mb-3">
                        <div class="card h-100 <?= getStatusCardClass($status) ?>">
                            <div class="card-body text-center">
                                <h3 class="display-4"><?= number_format($count, 0, ',', ' ') ?></h3>
                                <p class="card-text"><?= getStatusLabel($status) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Graphiques -->
            <div class="row mb-4">
                <!-- Graphique par catégorie -->
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-header">
                            <h3 class="card-title">Archives par catégorie</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($stats['by_category'])): ?>
                                <canvas id="categoryChart" width="400" height="300"></canvas>
                            <?php else: ?>
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Aucune donnée disponible pour ce graphique.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Graphique par statut -->
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-header">
                            <h3 class="card-title">Archives par statut</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($stats['by_status'])): ?>
                                <canvas id="statusChart" width="400" height="300"></canvas>
                            <?php else: ?>
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Aucune donnée disponible pour ce graphique.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tableau des archives -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0"><i class="fas fa-table me-2"></i>Liste des archives</h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($archives)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Référence</th>
                                        <th>Titre</th>
                                        <th>Catégorie</th>
                                        <th>Statut</th>
                                        <th>Date de création</th>
                                        <th>Téléchargements</th>
                                        <th>Taille</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($archives as $archive): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($archive['reference']) ?></td>
                                            <td><?= htmlspecialchars($archive['title']) ?></td>
                                            <td><?= htmlspecialchars($archive['category_name']) ?></td>
                                            <td>
                                                <span class="badge <?= getStatusBadgeClass($archive['status']) ?>">
                                                    <?= getStatusLabel($archive['status']) ?>
                                                </span>
                                            </td>
                                            <td><?= formatDate($archive['created_at']) ?></td>
                                            <td><?= number_format($archive['download_count'], 0, ',', ' ') ?></td>
                                            <td><?= formatFileSize($archive['file_size']) ?></td>
                                            <td>
                                                <a href="<?= BASE_URL ?>archives/view/<?= $archive['id'] ?>" class="btn btn-sm btn-primary" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= BASE_URL ?>archives/download/<?= $archive['id'] ?>" class="btn btn-sm btn-success" title="Télécharger">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Aucune archive trouvée pour les critères sélectionnés.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Fonction utilitaire pour générer des couleurs aléatoires
    function getRandomColors(count) {
        const colors = [];
        for (let i = 0; i < count; i++) {
            const r = Math.floor(Math.random() * 200);
            const g = Math.floor(Math.random() * 200);
            const b = Math.floor(Math.random() * 200);
            colors.push(`rgba(${r}, ${g}, ${b}, 0.7)`);
        }
        return colors;
    }
    
    // Graphique par catégorie
    <?php if (!empty($stats['by_category'])): ?>
    document.addEventListener('DOMContentLoaded', function() {
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        const categoryData = {
            labels: [<?php 
                $labels = [];
                foreach ($stats['by_category'] as $category) {
                    $labels[] = "'" . addslashes($category['name']) . "'";
                }
                echo implode(', ', $labels);
            ?>],
            datasets: [{
                label: 'Nombre d\'archives',
                data: [<?php 
                    $counts = [];
                    foreach ($stats['by_category'] as $category) {
                        $counts[] = $category['count'];
                    }
                    echo implode(', ', $counts);
                ?>],
                backgroundColor: getRandomColors(<?= count($stats['by_category']) ?>),
                borderWidth: 1
            }]
        };
        
        new Chart(categoryCtx, {
            type: 'pie',
            data: categoryData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    });
    <?php endif; ?>
    
    // Graphique par statut
    <?php if (!empty($stats['by_status'])): ?>
    document.addEventListener('DOMContentLoaded', function() {
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusData = {
            labels: [<?php 
                $labels = [];
                foreach ($stats['by_status'] as $status => $count) {
                    $labels[] = "'" . getStatusLabel($status) . "'";
                }
                echo implode(', ', $labels);
            ?>],
            datasets: [{
                label: 'Nombre d\'archives',
                data: [<?= implode(', ', array_values($stats['by_status'])) ?>],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.7)',    // active - vert
                    'rgba(108, 117, 125, 0.7)',  // inactive - gris
                    'rgba(0, 123, 255, 0.7)'     // archived - bleu
                ],
                borderWidth: 1
            }]
        };
        
        new Chart(statusCtx, {
            type: 'doughnut',
            data: statusData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    });
    <?php endif; ?>
</script>

<?php
/**
 * Retourne la classe CSS pour la carte de statut
 */
function getStatusCardClass($status) {
    switch ($status) {
        case 'active':
            return 'bg-success text-white';
        case 'inactive':
            return 'bg-secondary text-white';
        case 'archived':
            return 'bg-info text-white';
        default:
            return 'bg-light';
    }
}

/**
 * Retourne la classe CSS pour le badge de statut
 */
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'active':
            return 'bg-success';
        case 'inactive':
            return 'bg-secondary';
        case 'archived':
            return 'bg-info';
        default:
            return 'bg-light';
    }
}

/**
 * Retourne le libellé du statut
 */
function getStatusLabel($status) {
    switch ($status) {
        case 'active':
            return 'Actif';
        case 'inactive':
            return 'Inactif';
        case 'archived':
            return 'Archivé';
        default:
            return 'Inconnu';
    }
}
?>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>