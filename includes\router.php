<?php
/**
 * Classe Router - Gère le routage des requêtes HTTP
 */
class Router {
    private $routes = [];
    private $defaultController = 'HomeController';
    private $defaultAction = 'index';
    
    /**
     * Constructeur
     */
    public function __construct() {
        $this->initRoutes();
    }
    
    /**
     * Initialise les routes de l'application
     */
    private function initRoutes() {
        // Routes pour la page d'accueil
        $this->addRoute('', 'HomeController', 'index');
        $this->addRoute('home', 'HomeController', 'index');
        
        // Routes pour l'authentification
        $this->addRoute('login', 'AuthController', 'login');
        $this->addRoute('authenticate', 'AuthController', 'authenticate');
        $this->addRoute('logout', 'AuthController', 'logout');
        $this->addRoute('register', 'AuthController', 'register');
        $this->addRoute('store', 'AuthController', 'store');
        $this->addRoute('forgot-password', 'AuthController', 'forgotPassword');
        $this->addRoute('send-reset-link', 'AuthController', 'sendResetLink');
        $this->addRoute('reset-password', 'AuthController', 'resetPassword');
        $this->addRoute('update-password', 'AuthController', 'updatePassword');
        $this->addRoute('profile', 'UserController', 'profile');
        $this->addRoute('profile/update', 'UserController', 'updateProfile');
        
        // Routes pour les archives
        $this->addRoute('archives', 'ArchiveController', 'index');
        $this->addRoute('archives/create', 'ArchiveController', 'create');
        $this->addRoute('archives/store', 'ArchiveController', 'store');
        $this->addRoute('archives/view', 'ArchiveController', 'view');
        $this->addRoute('archives/edit', 'ArchiveController', 'edit');
        $this->addRoute('archives/update', 'ArchiveController', 'update');
        $this->addRoute('archives/delete', 'ArchiveController', 'delete');
        $this->addRoute('archives/download', 'ArchiveController', 'download');
        $this->addRoute('archives/dashboard', 'ArchiveController', 'dashboard');
        $this->addRoute('archives/search', 'ArchiveController', 'search');
        
        // Routes pour les utilisateurs (admin)
        $this->addRoute('users', 'UserController', 'index');
        $this->addRoute('users/create', 'UserController', 'create');
        $this->addRoute('users/store', 'UserController', 'store');
        $this->addRoute('users/show', 'UserController', 'show');
        $this->addRoute('users/edit', 'UserController', 'edit');
        $this->addRoute('users/update', 'UserController', 'update');
        $this->addRoute('users/delete', 'UserController', 'delete');
        
        // Routes pour les catégories
        $this->addRoute('categories', 'CategoryController', 'index');
        $this->addRoute('categories/create', 'CategoryController', 'create');
        $this->addRoute('categories/store', 'CategoryController', 'store');
        $this->addRoute('categories/edit', 'CategoryController', 'edit');
        $this->addRoute('categories/update', 'CategoryController', 'update');
        $this->addRoute('categories/delete', 'CategoryController', 'delete');
        
        // Routes pour les rapports
        $this->addRoute('reports', 'ReportController', 'index');
        $this->addRoute('reports/archives', 'ReportController', 'archives');
        $this->addRoute('reports/users', 'ReportController', 'users');
        $this->addRoute('reports/activities', 'ReportController', 'activities');
        
        // Routes pour les pages statiques
        $this->addRoute('about', 'PageController', 'about');
        $this->addRoute('contact', 'PageController', 'contact');
        $this->addRoute('send-contact', 'HomeController', 'sendContact');
    }
    
    /**
     * Ajoute une route
     * @param string $url L'URL de la route
     * @param string $controller Le contrôleur à utiliser
     * @param string $action L'action à exécuter
     */
    public function addRoute($url, $controller, $action) {
        $this->routes[$url] = [
            'controller' => $controller,
            'action' => $action
        ];
    }
    
    /**
     * Route la requête vers le contrôleur et l'action appropriés
     */
    public function route() {
        // Démarrer la session si elle n'est pas déjà démarrée
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        // Obtenir l'URL demandée
        $url = $this->getRequestUrl();
        
        // Trouver la route correspondante
        if (isset($this->routes[$url])) {
            $controller = $this->routes[$url]['controller'];
            $action = $this->routes[$url]['action'];
        } else {
            // Route par défaut si aucune correspondance n'est trouvée
            $controller = $this->defaultController;
            $action = $this->defaultAction;
        }
        
        // Charger le contrôleur
        $this->loadController($controller, $action);
    }
    
    /**
     * Obtient l'URL demandée à partir de la requête
     * @return string L'URL demandée
     */
    private function getRequestUrl() {
        $url = isset($_GET['url']) ? $_GET['url'] : '';
        $url = rtrim($url, '/');
        $url = filter_var($url, FILTER_SANITIZE_URL);
        return $url;
    }
    
    /**
     * Charge le contrôleur et exécute l'action
     * @param string $controllerName Le nom du contrôleur
     * @param string $action L'action à exécuter
     */
    private function loadController($controllerName, $action) {
        // Chemin du fichier du contrôleur
        $controllerFile = CONTROLLERS_PATH . $controllerName . '.php';
        
        // Vérifier si le fichier du contrôleur existe
        if (file_exists($controllerFile)) {
            // Inclure le fichier du contrôleur
            require_once $controllerFile;
            
            // Instancier le contrôleur
            $controller = new $controllerName();
            
            // Vérifier si l'action existe
            if (method_exists($controller, $action)) {
                // Exécuter l'action
                $controller->$action();
            } else {
                // Action non trouvée
                $this->handleError(404, "Action '$action' non trouvée dans le contrôleur '$controllerName'");
            }
        } else {
            // Contrôleur non trouvé
            $this->handleError(404, "Contrôleur '$controllerName' non trouvé");
        }
    }
    
    /**
     * Gère les erreurs
     * @param int $code Le code d'erreur HTTP
     * @param string $message Le message d'erreur
     */
    private function handleError($code, $message) {
        // Définir le code d'état HTTP
        http_response_code($code);
        
        // Afficher la page d'erreur
        include VIEWS_PATH . 'errors/error.php';
        exit;
    }
}
?>