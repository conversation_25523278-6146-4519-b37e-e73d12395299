<?php
/**
 * Contrôleur pour les pages statiques
 */
class PageController extends BaseController {
    /**
     * Affiche la page À propos
     */
    public function about() {
        $data = [
            'title' => 'À propos',
            'description' => 'Informations sur l\'application de gestion d\'archives'
        ];
        
        $this->view('pages/about', $data);
    }
    
    /**
     * Affiche la page de contact
     */
    public function contact() {
        // Traitement du formulaire de contact
        $errors = [];
        $success = false;
        
        if ($this->isPostRequest()) {
            // Vérifier le jeton CSRF
            if (!$this->validateCsrfToken()) {
                $this->redirect('contact');
            }
            
            // Récupérer les données du formulaire
            $name = trim($_POST['name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $subject = trim($_POST['subject'] ?? '');
            $message = trim($_POST['message'] ?? '');
            
            // Valider les données
            $rules = [
                'name' => 'required|max:100',
                'email' => 'required|email|max:100',
                'subject' => 'required|max:200',
                'message' => 'required|min:10|max:2000'
            ];
            
            $errors = $this->validate($rules, [
                'name' => $name,
                'email' => $email,
                'subject' => $subject,
                'message' => $message
            ]);
            
            // Si aucune erreur, traiter le message
            if (empty($errors)) {
                // Dans une application réelle, envoyer un email ou enregistrer en base de données
                // Pour cet exemple, on simule juste un succès
                $success = true;
                $this->setFlash('success', 'Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.');
                $this->redirect('contact');
            }
        }
        
        $data = [
            'title' => 'Contact',
            'description' => 'Contactez-nous pour toute question ou suggestion',
            'errors' => $errors,
            'success' => $success,
            'old' => $_POST ?? []
        ];
        
        $this->view('pages/contact', $data);
    }
}
?>