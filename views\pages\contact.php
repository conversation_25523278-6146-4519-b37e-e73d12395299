<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4"><i class="fas fa-envelope me-2"></i>Contact</h1>
            
            <div class="row">
                <!-- Informations de contact -->
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h3 class="card-title">Nos coordonnées</h3>
                            <hr>
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5>Adresse</h5>
                                    <p class="mb-0">123 Rue des Archives<br>75000 Paris<br>France</p>
                                </div>
                            </div>
                            
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-phone fa-2x text-primary"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5>Téléphone</h5>
                                    <p class="mb-0">+33 (0)1 23 45 67 89</p>
                                </div>
                            </div>
                            
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-envelope fa-2x text-primary"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5>Email</h5>
                                    <p class="mb-0"><EMAIL></p>
                                </div>
                            </div>
                            
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-clock fa-2x text-primary"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5>Horaires</h5>
                                    <p class="mb-0">Lundi - Vendredi: 9h00 - 18h00<br>Samedi - Dimanche: Fermé</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Formulaire de contact -->
                <div class="col-md-8 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="card-title">Envoyez-nous un message</h3>
                            <hr>
                            
                            <?php if (isset($success) && $success): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.
                                </div>
                            <?php endif; ?>
                            
                            <form action="<?= BASE_URL ?>send-contact" method="post">
                                <!-- Token CSRF -->
                                <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">
                                
                                <div class="row mb-3">
                                    <div class="col-md-6 mb-3 mb-md-0">
                                        <label for="name" class="form-label">Nom complet <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
                                               id="name" name="name" value="<?= htmlspecialchars($old['name'] ?? '') ?>" required>
                                        <?php if (isset($errors['name'])): ?>
                                            <div class="invalid-feedback"><?= $errors['name'] ?></div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                               id="email" name="email" value="<?= htmlspecialchars($old['email'] ?? '') ?>" required>
                                        <?php if (isset($errors['email'])): ?>
                                            <div class="invalid-feedback"><?= $errors['email'] ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Sujet <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?= isset($errors['subject']) ? 'is-invalid' : '' ?>" 
                                           id="subject" name="subject" value="<?= htmlspecialchars($old['subject'] ?? '') ?>" required>
                                    <?php if (isset($errors['subject'])): ?>
                                        <div class="invalid-feedback"><?= $errors['subject'] ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                                    <textarea class="form-control <?= isset($errors['message']) ? 'is-invalid' : '' ?>" 
                                              id="message" name="message" rows="5" required><?= htmlspecialchars($old['message'] ?? '') ?></textarea>
                                    <?php if (isset($errors['message'])): ?>
                                        <div class="invalid-feedback"><?= $errors['message'] ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo me-2"></i>Réinitialiser
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>Envoyer
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Carte Google Maps (simulée) -->
            <div class="card mb-4">
                <div class="card-body p-0">
                    <div class="ratio ratio-21x9">
                        <div class="bg-light d-flex align-items-center justify-content-center">
                            <div class="text-center">
                                <i class="fas fa-map-marked-alt fa-4x text-primary mb-3"></i>
                                <h4>Carte Google Maps</h4>
                                <p class="text-muted">Une carte interactive serait affichée ici dans une application réelle.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <a href="<?= BASE_URL ?>" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>Retour à l'accueil
                </a>
                <a href="<?= BASE_URL ?>about" class="btn btn-outline-primary ms-2">
                    <i class="fas fa-info-circle me-2"></i>À propos
                </a>
            </div>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>