<?php
/**
 * Point d'entrée principal de l'application de gestion d'archives
 * Ce fichier initialise l'application et gère le routage des requêtes
 */

// Définir le fuseau horaire
date_default_timezone_set('Europe/Paris');

// Charger les configurations
require_once 'config/config.php';
require_once 'config/database.php';

// Charger les utilitaires et le routeur
require_once 'includes/utils.php';
require_once 'includes/router.php';

// Démarrer la session
session_start();

// Créer une instance du routeur
$router = new Router();

// Traiter la requête actuelle
$router->route();