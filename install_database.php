<?php
/**
 * Script d'installation robuste de la base de données
 */

// Configuration de la base de données
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'archive_db';

echo "<h1>Installation de la base de données Archive</h1>";

try {
    // Connexion à MySQL sans spécifier de base de données
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✓ Connexion à MySQL réussie</p>";
    
    // Créer la base de données
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✓ Base de données '$database' créée</p>";
    
    // Se connecter à la base de données
    $pdo->exec("USE `$database`");
    echo "<p>✓ Connexion à la base de données '$database'</p>";
    
    // Créer la table users
    $createUsers = "
    CREATE TABLE IF NOT EXISTS `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `email` varchar(150) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `role` enum('admin','user') NOT NULL DEFAULT 'user',
        `status` enum('active','inactive') NOT NULL DEFAULT 'active',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_email` (`email`),
        KEY `idx_role` (`role`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createUsers);
    echo "<p>✓ Table 'users' créée</p>";
    
    // Créer la table categories
    $createCategories = "
    CREATE TABLE IF NOT EXISTS `categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `description` text,
        `status` enum('active','inactive') NOT NULL DEFAULT 'active',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_name` (`name`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createCategories);
    echo "<p>✓ Table 'categories' créée</p>";
    
    // Créer la table archives
    $createArchives = "
    CREATE TABLE IF NOT EXISTS `archives` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `reference_number` varchar(50) NOT NULL UNIQUE,
        `title` varchar(255) NOT NULL,
        `description` text,
        `category_id` int(11) DEFAULT NULL,
        `file_path` varchar(500) NOT NULL,
        `file_name` varchar(255) NOT NULL,
        `file_size` bigint(20) NOT NULL,
        `mime_type` varchar(100) NOT NULL,
        `status` enum('active','archived','deleted') NOT NULL DEFAULT 'active',
        `created_by` int(11) NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_reference` (`reference_number`),
        KEY `idx_category` (`category_id`),
        KEY `idx_created_by` (`created_by`),
        KEY `idx_status` (`status`),
        KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createArchives);
    echo "<p>✓ Table 'archives' créée</p>";
    
    // Créer la table activities
    $createActivities = "
    CREATE TABLE IF NOT EXISTS `activities` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `action` varchar(50) NOT NULL,
        `description` text NOT NULL,
        `ip_address` varchar(45) DEFAULT NULL,
        `user_agent` text,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_action` (`action`),
        KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createActivities);
    echo "<p>✓ Table 'activities' créée</p>";
    
    // Ajouter les contraintes de clés étrangères après création des tables
    try {
        $pdo->exec("ALTER TABLE `archives` ADD CONSTRAINT `fk_archives_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL");
        echo "<p>✓ Contrainte archives->categories ajoutée</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'already exists') === false) {
            echo "<p style='color: orange;'>⚠ Contrainte archives->categories: " . $e->getMessage() . "</p>";
        }
    }
    
    try {
        $pdo->exec("ALTER TABLE `archives` ADD CONSTRAINT `fk_archives_user` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE");
        echo "<p>✓ Contrainte archives->users ajoutée</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'already exists') === false) {
            echo "<p style='color: orange;'>⚠ Contrainte archives->users: " . $e->getMessage() . "</p>";
        }
    }
    
    try {
        $pdo->exec("ALTER TABLE `activities` ADD CONSTRAINT `fk_activities_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE");
        echo "<p>✓ Contrainte activities->users ajoutée</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'already exists') === false) {
            echo "<p style='color: orange;'>⚠ Contrainte activities->users: " . $e->getMessage() . "</p>";
        }
    }
    
    // Créer l'utilisateur administrateur par défaut
    $adminEmail = '<EMAIL>';
    $adminPassword = password_hash('password', PASSWORD_DEFAULT);
    
    // Vérifier si l'admin existe déjà
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$adminEmail]);
    
    if (!$stmt->fetch()) {
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, 'admin', 'active')");
        $stmt->execute(['Administrateur', $adminEmail, $adminPassword]);
        echo "<p>✓ Utilisateur administrateur créé</p>";
        echo "<p><strong>Email:</strong> $adminEmail</p>";
        echo "<p><strong>Mot de passe:</strong> password</p>";
    } else {
        echo "<p>✓ Utilisateur administrateur déjà existant</p>";
    }
    
    // Créer les catégories par défaut
    $categories = [
        ['Documents administratifs', 'Documents relatifs à l\'administration'],
        ['Contrats', 'Contrats et accords'],
        ['Factures', 'Factures et documents comptables'],
        ['Correspondance', 'Lettres et emails'],
        ['Rapports', 'Rapports et analyses']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO categories (name, description, status) VALUES (?, ?, 'active')");
    $categoriesCreated = 0;
    
    foreach ($categories as $category) {
        if ($stmt->execute($category)) {
            $categoriesCreated++;
        }
    }
    
    echo "<p>✓ Catégories par défaut créées</p>";
    
    // Créer le dossier uploads s'il n'existe pas
    $uploadsDir = 'uploads';
    if (!is_dir($uploadsDir)) {
        if (mkdir($uploadsDir, 0755, true)) {
            echo "<p>✓ Dossier uploads créé</p>";
        } else {
            echo "<p style='color: red;'>✗ Impossible de créer le dossier uploads</p>";
        }
    } else {
        echo "<p>✓ Dossier uploads existe</p>";
    }
    
    // Créer le fichier .htaccess pour protéger le dossier uploads
    $htaccessContent = "Options -Indexes\nDeny from all\n<Files ~ \"\\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|jpg|jpeg|png|gif)$\">\n    Allow from all\n</Files>";
    file_put_contents($uploadsDir . '/.htaccess', $htaccessContent);
    echo "<p>✓ Protection du dossier uploads configurée</p>";
    
    echo "<h2 style='color: green;'>✅ Installation terminée avec succès!</h2>";
    echo "<p><a href='check_installation.php'>Vérifier l'installation</a></p>";
    echo "<p><a href='index.php'>Accéder à l'application</a></p>";
    echo "<p><a href='index.php?url=login'>Se connecter</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Erreur: " . $e->getMessage() . "</p>";
    echo "<h3>Vérifications à effectuer:</h3>";
    echo "<ul>";
    echo "<li>MySQL est-il démarré dans Laragon?</li>";
    echo "<li>Les paramètres de connexion sont-ils corrects?</li>";
    echo "<li>L'utilisateur MySQL a-t-il les permissions nécessaires?</li>";
    echo "</ul>";
}
?>
