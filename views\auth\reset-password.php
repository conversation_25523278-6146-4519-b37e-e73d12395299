<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Réinitialisation du mot de passe</h4>
            </div>
            <div class="card-body">
                <p class="mb-4">Veuillez entrer votre nouveau mot de passe.</p>

                <form action="<?= BASE_URL ?>update-password" method="post">
                    <!-- Jeton CSRF -->
                    <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">
                    <input type="hidden" name="token" value="<?= htmlspecialchars($_GET['token'] ?? '') ?>">

                    <div class="mb-3">
                        <label for="password" class="form-label">Nouveau mot de passe</label>
                        <input type="password"
                            class="form-control <?= isset($_SESSION['form_errors']['password']) ? 'is-invalid' : '' ?>"
                            id="password" name="password" required>
                        <?php if (isset($_SESSION['form_errors']['password'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['password']) ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="password_confirm" class="form-label">Confirmer le nouveau mot de passe</label>
                        <input type="password"
                            class="form-control <?= isset($_SESSION['form_errors']['password_confirm']) ? 'is-invalid' : '' ?>"
                            id="password_confirm" name="password_confirm" required>
                        <?php if (isset($_SESSION['form_errors']['password_confirm'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($_SESSION['form_errors']['password_confirm']) ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key me-2"></i>Réinitialiser le mot de passe
                        </button>
                    </div>
                </form>

                <div class="mt-3 text-center">
                    <p>
                        <a href="<?= BASE_URL ?>login">Retour à la connexion</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
// Nettoyer les données de formulaire et les erreurs après l'affichage
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);
?>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>