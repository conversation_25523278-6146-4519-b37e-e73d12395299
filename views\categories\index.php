<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-folder me-2"></i>Gestion des catégories</h1>
        <?php if (isAdmin()): ?>
            <a href="<?= BASE_URL ?>categories/create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Nouvelle catégorie
            </a>
        <?php endif; ?>
    </div>
    
    <!-- Filtres de recherche -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>Filtres de recherche
            </h5>
        </div>
        <div class="card-body">
            <form action="<?= BASE_URL ?>categories" method="get" id="filter-form">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="search" class="form-label">Recherche</label>
                        <input type="text" class="form-control" id="search" name="search" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" placeholder="Nom ou description">
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <div class="d-grid gap-2 w-100">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Rechercher
                            </button>
                            <button type="button" id="reset-filters" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i>Réinitialiser
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Résultats -->
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Liste des catégories
                </h5>
                <span class="badge bg-primary"><?= $total ?> résultat(s)</span>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (count($categories) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover table-striped mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Description</th>
                                <th>Archives</th>
                                <th>Date de création</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($categories as $category): ?>
                                <tr>
                                    <td><?= $category['id'] ?></td>
                                    <td><?= htmlspecialchars($category['name']) ?></td>
                                    <td><?= htmlspecialchars(truncateText($category['description'], 50)) ?></td>
                                    <td><?= $category['archive_count'] ?></td>
                                    <td><?= formatDate($category['created_at']) ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= BASE_URL ?>archives?category_id=<?= $category['id'] ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="Voir les archives">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (isAdmin()): ?>
                                                <a href="<?= BASE_URL ?>categories/edit/<?= $category['id'] ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?= BASE_URL ?>categories/delete" method="post" class="d-inline">
                                                    <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">
                                                    <input type="hidden" name="id" value="<?= $category['id'] ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger delete-confirm" data-bs-toggle="tooltip" title="Supprimer">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <nav aria-label="Navigation des pages">
                            <ul class="pagination">
                                <?php if ($current_page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= BASE_URL ?>categories?page=<?= $current_page - 1 ?><?= $query_string ?>" aria-label="Précédent">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                    <li class="page-item <?= $i == $current_page ? 'active' : '' ?>">
                                        <a class="page-link" href="<?= BASE_URL ?>categories?page=<?= $i ?><?= $query_string ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($current_page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= BASE_URL ?>categories?page=<?= $current_page + 1 ?><?= $query_string ?>" aria-label="Suivant">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="alert alert-info m-3">
                    <i class="fas fa-info-circle me-2"></i>Aucune catégorie trouvée.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>