<?php
require_once CONTROLLERS_PATH . 'BaseController.php';
require_once MODELS_PATH . 'UserModel.php';

/**
 * Contrôleur pour l'authentification
 */
class AuthController extends BaseController
{
    private $userModel;

    /**
     * Constructeur
     */
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new UserModel();
    }

    /**
     * Affiche le formulaire de connexion
     */
    public function login()
    {
        // Si l'utilisateur est déjà connecté, rediriger vers la page d'accueil
        if (isLoggedIn()) {
            $this->redirect('home');
        }

        // Données à passer à la vue
        $data = [
            'title' => 'Connexion - ' . APP_NAME,
            'description' => 'Connectez-vous à votre compte'
        ];

        // Charger la vue
        $this->view('auth/login', $data);
    }

    /**
     * Traite le formulaire de connexion
     */
    public function authenticate()
    {
        // Vérifier si c'est une requête POST
        if (!$this->isPostRequest()) {
            $this->redirect('login');
        }

        // Vérifier le jeton CSRF
        if (!$this->validateCsrfToken()) {
            $this->redirect('login');
        }

        // Récupérer et nettoyer les données du formulaire
        $email = sanitize($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);

        // Valider les données
        $errors = [];

        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'L\'adresse email est invalide';
        }

        if (empty($password)) {
            $errors[] = 'Le mot de passe est requis';
        }

        // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
        if (!empty($errors)) {
            $_SESSION['login_errors'] = $errors;
            $_SESSION['login_form'] = [
                'email' => $email
            ];
            $this->redirect('login');
        }

        // Vérifier les identifiants
        $user = $this->userModel->findByEmail($email);

        if (!$user || !password_verify($password, $user['password'])) {
            $this->setFlash('error', 'Identifiants incorrects');
            $_SESSION['login_form'] = [
                'email' => $email
            ];
            $this->redirect('login');
        }

        // Vérifier si le compte est actif
        if ($user['status'] !== 'active') {
            $this->setFlash('error', 'Votre compte est désactivé. Veuillez contacter l\'administrateur.');
            $this->redirect('login');
        }

        // Connexion réussie, enregistrer les informations de l'utilisateur en session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];

        // Mettre à jour la dernière connexion
        $this->userModel->updateLastLogin($user['id']);

        // Si l'option "Se souvenir de moi" est cochée, créer un cookie
        if ($remember) {
            $token = generateRandomString(32);
            $expiry = time() + (30 * 24 * 60 * 60); // 30 jours

            // Enregistrer le token en base de données
            $this->userModel->saveRememberToken($user['id'], $token, $expiry);

            // Créer le cookie
            setcookie('remember_token', $token, $expiry, '/', '', false, true);
        }

        // Définir un message de succès
        $this->setFlash('success', 'Vous êtes maintenant connecté');

        // Rediriger vers la page d'accueil ou la page demandée précédemment
        $redirect = $_SESSION['redirect_url'] ?? 'home';
        unset($_SESSION['redirect_url']);
        $this->redirect($redirect);
    }

    /**
     * Déconnecte l'utilisateur
     */
    public function logout()
    {
        // Supprimer le token de rappel si présent
        if (isset($_COOKIE['remember_token'])) {
            $token = $_COOKIE['remember_token'];
            $this->userModel->deleteRememberToken($token);
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }

        // Détruire la session
        session_unset();
        session_destroy();

        // Redémarrer la session pour les messages flash
        session_start();

        // Définir un message de succès
        $this->setFlash('success', 'Vous avez été déconnecté avec succès');

        // Rediriger vers la page de connexion
        $this->redirect('login');
    }

    /**
     * Affiche le formulaire d'inscription
     */
    public function register()
    {
        // Si l'utilisateur est déjà connecté, rediriger vers la page d'accueil
        if (isLoggedIn()) {
            $this->redirect('home');
        }

        // Données à passer à la vue
        $data = [
            'title' => 'Inscription - ' . APP_NAME,
            'description' => 'Créez un nouveau compte'
        ];

        // Charger la vue
        $this->view('auth/register', $data);
    }

    /**
     * Traite le formulaire d'inscription
     */
    public function store()
    {
        // Vérifier si c'est une requête POST
        if (!$this->isPostRequest()) {
            $this->redirect('register');
        }

        // Vérifier le jeton CSRF
        if (!$this->validateCsrfToken()) {
            $this->redirect('register');
        }

        // Récupérer et nettoyer les données du formulaire
        $name = sanitize($_POST['name'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';

        // Valider les données
        $errors = [];

        if (empty($name)) {
            $errors[] = 'Le nom est requis';
        }

        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'L\'adresse email est invalide';
        } else if ($this->userModel->emailExists($email)) {
            $errors[] = 'Cette adresse email est déjà utilisée';
        }

        if (empty($password)) {
            $errors[] = 'Le mot de passe est requis';
        } else if (strlen($password) < 8) {
            $errors[] = 'Le mot de passe doit contenir au moins 8 caractères';
        }

        if ($password !== $confirmPassword) {
            $errors[] = 'Les mots de passe ne correspondent pas';
        }

        // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
        if (!empty($errors)) {
            $_SESSION['register_errors'] = $errors;
            $_SESSION['register_form'] = [
                'name' => $name,
                'email' => $email
            ];
            $this->redirect('register');
        }

        // Hacher le mot de passe
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Créer l'utilisateur
        $userId = $this->userModel->create([
            'name' => $name,
            'email' => $email,
            'password' => $hashedPassword,
            'role' => 'user', // Par défaut, les nouveaux utilisateurs ont le rôle 'user'
            'status' => 'active' // Par défaut, les nouveaux utilisateurs sont actifs
        ]);

        if (!$userId) {
            $this->setFlash('error', 'Une erreur est survenue lors de la création du compte');
            $_SESSION['register_form'] = [
                'name' => $name,
                'email' => $email
            ];
            $this->redirect('register');
        }

        // Définir un message de succès
        $this->setFlash('success', 'Votre compte a été créé avec succès. Vous pouvez maintenant vous connecter.');

        // Rediriger vers la page de connexion
        $this->redirect('login');
    }

    /**
     * Affiche le formulaire de réinitialisation de mot de passe
     */
    public function forgotPassword()
    {
        // Si l'utilisateur est déjà connecté, rediriger vers la page d'accueil
        if (isLoggedIn()) {
            $this->redirect('home');
        }

        // Données à passer à la vue
        $data = [
            'title' => 'Mot de passe oublié - ' . APP_NAME,
            'description' => 'Réinitialisez votre mot de passe'
        ];

        // Charger la vue
        $this->view('auth/forgot_password', $data);
    }

    /**
     * Traite le formulaire de réinitialisation de mot de passe
     */
    public function sendResetLink()
    {
        // Vérifier si c'est une requête POST
        if (!$this->isPostRequest()) {
            $this->redirect('forgot-password');
        }

        // Vérifier le jeton CSRF
        if (!$this->validateCsrfToken()) {
            $this->redirect('forgot-password');
        }

        // Récupérer et nettoyer les données du formulaire
        $email = sanitize($_POST['email'] ?? '');

        // Valider les données
        $errors = [];

        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'L\'adresse email est invalide';
        }

        // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
        if (!empty($errors)) {
            $_SESSION['forgot_password_errors'] = $errors;
            $_SESSION['forgot_password_form'] = [
                'email' => $email
            ];
            $this->redirect('forgot-password');
        }

        // Vérifier si l'email existe
        $user = $this->userModel->findByEmail($email);

        if (!$user) {
            $this->setFlash('error', 'Aucun compte n\'est associé à cette adresse email');
            $_SESSION['forgot_password_form'] = [
                'email' => $email
            ];
            $this->redirect('forgot-password');
        }

        // Générer un token de réinitialisation
        $token = generateRandomString(32);
        $expiry = time() + (60 * 60); // 1 heure

        // Enregistrer le token en base de données
        $this->userModel->saveResetToken($user['id'], $token, $expiry);

        // Envoyer l'email de réinitialisation (à implémenter selon les besoins)
        // ...

        // Définir un message de succès
        $this->setFlash('success', 'Un email de réinitialisation a été envoyé à votre adresse email');

        // Rediriger vers la page de connexion
        $this->redirect('login');
    }

    /**
     * Affiche le formulaire de réinitialisation de mot de passe
     */
    public function resetPassword()
    {
        // Si l'utilisateur est déjà connecté, rediriger vers la page d'accueil
        if (isLoggedIn()) {
            $this->redirect('home');
        }

        // Récupérer le token
        $token = $_GET['token'] ?? '';

        if (empty($token)) {
            $this->setFlash('error', 'Token de réinitialisation invalide');
            $this->redirect('login');
        }

        // Vérifier si le token est valide
        $user = $this->userModel->findByResetToken($token);

        if (!$user) {
            $this->setFlash('error', 'Token de réinitialisation invalide ou expiré');
            $this->redirect('login');
        }

        // Données à passer à la vue
        $data = [
            'title' => 'Réinitialisation du mot de passe - ' . APP_NAME,
            'description' => 'Définissez un nouveau mot de passe',
            'token' => $token
        ];

        // Charger la vue
        $this->view('auth/reset_password', $data);
    }

    /**
     * Traite le formulaire de réinitialisation de mot de passe
     */
    public function updatePassword()
    {
        // Vérifier si c'est une requête POST
        if (!$this->isPostRequest()) {
            $this->redirect('login');
        }

        // Vérifier le jeton CSRF
        if (!$this->validateCsrfToken()) {
            $this->redirect('login');
        }

        // Récupérer et nettoyer les données du formulaire
        $token = sanitize($_POST['token'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';

        // Valider les données
        $errors = [];

        if (empty($token)) {
            $errors[] = 'Token de réinitialisation invalide';
        }

        if (empty($password)) {
            $errors[] = 'Le mot de passe est requis';
        } else if (strlen($password) < 8) {
            $errors[] = 'Le mot de passe doit contenir au moins 8 caractères';
        }

        if ($password !== $confirmPassword) {
            $errors[] = 'Les mots de passe ne correspondent pas';
        }

        // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
        if (!empty($errors)) {
            $_SESSION['reset_password_errors'] = $errors;
            $this->redirect('reset-password?token=' . $token);
        }

        // Vérifier si le token est valide
        $user = $this->userModel->findByResetToken($token);

        if (!$user) {
            $this->setFlash('error', 'Token de réinitialisation invalide ou expiré');
            $this->redirect('login');
        }

        // Hacher le nouveau mot de passe
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Mettre à jour le mot de passe
        $this->userModel->updatePassword($user['id'], $hashedPassword);

        // Supprimer le token de réinitialisation
        $this->userModel->deleteResetToken($token);

        // Définir un message de succès
        $this->setFlash('success', 'Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter.');

        // Rediriger vers la page de connexion
        $this->redirect('login');
    }
}
