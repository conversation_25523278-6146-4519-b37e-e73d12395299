<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-archive me-2"></i>Gestion des archives</h1>
        <?php if (isLoggedIn()): ?>
            <a href="<?= BASE_URL ?>archives/create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Nouvelle archive
            </a>
        <?php endif; ?>
    </div>
    
    <!-- Filtres de recherche -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>Filtres de recherche
            </h5>
        </div>
        <div class="card-body">
            <form action="<?= BASE_URL ?>archives" method="get" id="filter-form">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Recherche</label>
                        <input type="text" class="form-control" id="search" name="search" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" placeholder="Référence, titre ou description">
                    </div>
                    <div class="col-md-4">
                        <label for="category_id" class="form-label">Catégorie</label>
                        <select class="form-select auto-submit" id="category_id" name="category_id">
                            <option value="">Toutes les catégories</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>" <?= (isset($_GET['category_id']) && $_GET['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="status" class="form-label">Statut</label>
                        <select class="form-select auto-submit" id="status" name="status">
                            <option value="">Tous les statuts</option>
                            <option value="active" <?= (isset($_GET['status']) && $_GET['status'] == 'active') ? 'selected' : '' ?>>Actif</option>
                            <option value="inactive" <?= (isset($_GET['status']) && $_GET['status'] == 'inactive') ? 'selected' : '' ?>>Inactif</option>
                            <option value="archived" <?= (isset($_GET['status']) && $_GET['status'] == 'archived') ? 'selected' : '' ?>>Archivé</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="date_from" class="form-label">Date de début</label>
                        <input type="date" class="form-control auto-submit" id="date_from" name="date_from" value="<?= htmlspecialchars($_GET['date_from'] ?? '') ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="date_to" class="form-label">Date de fin</label>
                        <input type="date" class="form-control auto-submit" id="date_to" name="date_to" value="<?= htmlspecialchars($_GET['date_to'] ?? '') ?>">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <div class="d-grid gap-2 w-100">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Rechercher
                            </button>
                            <button type="button" id="reset-filters" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i>Réinitialiser
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Résultats -->
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Liste des archives
                </h5>
                <span class="badge bg-primary"><?= $total ?> résultat(s)</span>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (count($archives) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover table-striped mb-0">
                        <thead>
                            <tr>
                                <th>Référence</th>
                                <th>Titre</th>
                                <th>Catégorie</th>
                                <th>Date</th>
                                <th>Statut</th>
                                <th>Téléchargements</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($archives as $archive): ?>
                                <tr>
                                    <td><?= htmlspecialchars($archive['reference_number']) ?></td>
                                    <td><?= htmlspecialchars($archive['title']) ?></td>
                                    <td><?= htmlspecialchars($archive['category_name']) ?></td>
                                    <td><?= formatDate($archive['created_at']) ?></td>
                                    <td>
                                        <?php if ($archive['status'] == 'active'): ?>
                                            <span class="badge bg-success">Actif</span>
                                        <?php elseif ($archive['status'] == 'inactive'): ?>
                                            <span class="badge bg-secondary">Inactif</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning text-dark">Archivé</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $archive['download_count'] ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= BASE_URL ?>archives/view/<?= $archive['id'] ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="Voir les détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (isLoggedIn()): ?>
                                                <a href="<?= BASE_URL ?>archives/download/<?= $archive['id'] ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Télécharger">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <?php if (isAdmin()): ?>
                                                    <a href="<?= BASE_URL ?>archives/edit/<?= $archive['id'] ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Modifier">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?= BASE_URL ?>archives/delete" method="post" class="d-inline">
                                                        <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">
                                                        <input type="hidden" name="id" value="<?= $archive['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-danger delete-confirm" data-bs-toggle="tooltip" title="Supprimer">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <nav aria-label="Navigation des pages">
                            <ul class="pagination">
                                <?php if ($current_page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= BASE_URL ?>archives?page=<?= $current_page - 1 ?><?= $query_string ?>" aria-label="Précédent">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                    <li class="page-item <?= $i == $current_page ? 'active' : '' ?>">
                                        <a class="page-link" href="<?= BASE_URL ?>archives?page=<?= $i ?><?= $query_string ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($current_page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?= BASE_URL ?>archives?page=<?= $current_page + 1 ?><?= $query_string ?>" aria-label="Suivant">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="alert alert-info m-3">
                    <i class="fas fa-info-circle me-2"></i>Aucune archive trouvée.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>