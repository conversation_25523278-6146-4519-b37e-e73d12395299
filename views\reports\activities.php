<?php require_once VIEWS_PATH . 'templates/header.php'; ?>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= BASE_URL ?>">Accueil</a></li>
                    <li class="breadcrumb-item"><a href="<?= BASE_URL ?>reports">Rapports</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Activités</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-history me-2"></i>Journal des Activités</h1>
                
                <!-- Boutons d'exportation -->
                <div class="btn-group">
                    <a href="<?= BASE_URL ?>reports/activities?format=csv<?= isset($_GET['start_date']) ? '&start_date=' . $_GET['start_date'] : '' ?><?= isset($_GET['end_date']) ? '&end_date=' . $_GET['end_date'] : '' ?><?= isset($_GET['user_id']) ? '&user_id=' . $_GET['user_id'] : '' ?><?= isset($_GET['action']) ? '&action=' . $_GET['action'] : '' ?>" class="btn btn-success">
                        <i class="fas fa-file-csv me-2"></i>Exporter en CSV
                    </a>
                    <a href="<?= BASE_URL ?>reports/activities?format=pdf<?= isset($_GET['start_date']) ? '&start_date=' . $_GET['start_date'] : '' ?><?= isset($_GET['end_date']) ? '&end_date=' . $_GET['end_date'] : '' ?><?= isset($_GET['user_id']) ? '&user_id=' . $_GET['user_id'] : '' ?><?= isset($_GET['action']) ? '&action=' . $_GET['action'] : '' ?>" class="btn btn-danger">
                        <i class="fas fa-file-pdf me-2"></i>Exporter en PDF
                    </a>
                </div>
            </div>
            
            <!-- Filtres -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0"><i class="fas fa-filter me-2"></i>Filtres</h3>
                </div>
                <div class="card-body">
                    <form action="<?= BASE_URL ?>reports/activities" method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Date de début</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="<?= htmlspecialchars($options['date_from'] ?? date('Y-m-d', strtotime('-1 week'))) ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">Date de fin</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="<?= htmlspecialchars($options['date_to'] ?? date('Y-m-d')) ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="user_id" class="form-label">Utilisateur</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="0">Tous les utilisateurs</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?= $user['id'] ?>" <?= ($options['user_id'] == $user['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($user['username']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="action" class="form-label">Type d'action</label>
                            <select class="form-select" id="action" name="action">
                                <option value="">Toutes les actions</option>
                                <option value="login" <?= ($options['action'] == 'login') ? 'selected' : '' ?>>Connexion</option>
                                <option value="logout" <?= ($options['action'] == 'logout') ? 'selected' : '' ?>>Déconnexion</option>
                                <option value="create" <?= ($options['action'] == 'create') ? 'selected' : '' ?>>Création</option>
                                <option value="update" <?= ($options['action'] == 'update') ? 'selected' : '' ?>>Modification</option>
                                <option value="delete" <?= ($options['action'] == 'delete') ? 'selected' : '' ?>>Suppression</option>
                                <option value="download" <?= ($options['action'] == 'download') ? 'selected' : '' ?>>Téléchargement</option>
                            </select>
                        </div>
                        
                        <div class="col-md-12 d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Appliquer les filtres
                            </button>
                            <a href="<?= BASE_URL ?>reports/activities" class="btn btn-secondary ms-2">
                                <i class="fas fa-undo me-2"></i>Réinitialiser
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Graphiques -->
            <div class="row mb-4">
                <!-- Graphique par type d'action -->
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-header">
                            <h3 class="card-title">Activités par type d'action</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($activities)): ?>
                                <canvas id="actionTypeChart" width="400" height="300"></canvas>
                            <?php else: ?>
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Aucune donnée disponible pour ce graphique.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Graphique par jour -->
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-header">
                            <h3 class="card-title">Activités par jour</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($activities)): ?>
                                <canvas id="dailyActivityChart" width="400" height="300"></canvas>
                            <?php else: ?>
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Aucune donnée disponible pour ce graphique.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tableau des activités -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0"><i class="fas fa-table me-2"></i>Journal des activités</h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($activities)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Date et heure</th>
                                        <th>Utilisateur</th>
                                        <th>Action</th>
                                        <th>Description</th>
                                        <th>IP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($activities as $activity): ?>
                                        <tr>
                                            <td><?= formatDate($activity['created_at'], true) ?></td>
                                            <td>
                                                <?php if ($activity['user_id']): ?>
                                                    <a href="<?= BASE_URL ?>users/view/<?= $activity['user_id'] ?>">
                                                        <?= htmlspecialchars($activity['username']) ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">Système</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge <?= getActionBadgeClass($activity['action_type']) ?>">
                                                    <?= getActionLabel($activity['action_type']) ?>
                                                </span>
                                            </td>
                                            <td><?= htmlspecialchars($activity['description']) ?></td>
                                            <td><?= htmlspecialchars($activity['ip_address']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <?php if (count($activities) >= 1000): ?>
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Les résultats sont limités à 1000 entrées. Utilisez les filtres pour affiner votre recherche.
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Aucune activité trouvée pour les critères sélectionnés.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Fonction utilitaire pour générer des couleurs aléatoires
    function getRandomColors(count) {
        const colors = [];
        for (let i = 0; i < count; i++) {
            const r = Math.floor(Math.random() * 200);
            const g = Math.floor(Math.random() * 200);
            const b = Math.floor(Math.random() * 200);
            colors.push(`rgba(${r}, ${g}, ${b}, 0.7)`);
        }
        return colors;
    }
    
    <?php if (!empty($activities)): ?>
    // Préparer les données pour le graphique par type d'action
    const actionCounts = {};
    <?php foreach ($activities as $activity): ?>
        const actionType = '<?= $activity['action_type'] ?>';
        actionCounts[actionType] = (actionCounts[actionType] || 0) + 1;
    <?php endforeach; ?>
    
    // Graphique par type d'action
    document.addEventListener('DOMContentLoaded', function() {
        const actionTypeCtx = document.getElementById('actionTypeChart').getContext('2d');
        const actionLabels = Object.keys(actionCounts).map(action => getActionLabel(action));
        const actionData = Object.values(actionCounts);
        
        const actionTypeData = {
            labels: actionLabels,
            datasets: [{
                label: 'Nombre d\'activités',
                data: actionData,
                backgroundColor: getRandomColors(actionLabels.length),
                borderWidth: 1
            }]
        };
        
        new Chart(actionTypeCtx, {
            type: 'pie',
            data: actionTypeData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    });
    
    // Préparer les données pour le graphique par jour
    const dailyCounts = {};
    <?php foreach ($activities as $activity): ?>
        const day = '<?= date('Y-m-d', strtotime($activity['created_at'])) ?>';
        dailyCounts[day] = (dailyCounts[day] || 0) + 1;
    <?php endforeach; ?>
    
    // Trier les jours
    const sortedDays = Object.keys(dailyCounts).sort();
    
    // Graphique par jour
    document.addEventListener('DOMContentLoaded', function() {
        const dailyActivityCtx = document.getElementById('dailyActivityChart').getContext('2d');
        
        const dailyActivityData = {
            labels: sortedDays.map(day => formatDate(day)),
            datasets: [{
                label: 'Nombre d\'activités',
                data: sortedDays.map(day => dailyCounts[day]),
                backgroundColor: 'rgba(0, 123, 255, 0.5)',
                borderColor: 'rgba(0, 123, 255, 1)',
                borderWidth: 1,
                tension: 0.1
            }]
        };
        
        new Chart(dailyActivityCtx, {
            type: 'line',
            data: dailyActivityData,
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            title: function(tooltipItems) {
                                return formatDate(tooltipItems[0].label);
                            }
                        }
                    }
                }
            }
        });
    });
    <?php endif; ?>
    
    // Fonction pour formater les dates
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR');
    }
    
    // Fonction pour obtenir le libellé d'une action
    function getActionLabel(action) {
        switch (action) {
            case 'login': return 'Connexion';
            case 'logout': return 'Déconnexion';
            case 'create': return 'Création';
            case 'update': return 'Modification';
            case 'delete': return 'Suppression';
            case 'download': return 'Téléchargement';
            default: return action;
        }
    }
</script>

<?php
/**
 * Retourne la classe CSS pour le badge d'action
 */
function getActionBadgeClass($action) {
    switch ($action) {
        case 'login':
            return 'bg-success';
        case 'logout':
            return 'bg-secondary';
        case 'create':
            return 'bg-primary';
        case 'update':
            return 'bg-warning text-dark';
        case 'delete':
            return 'bg-danger';
        case 'download':
            return 'bg-info text-dark';
        default:
            return 'bg-light text-dark';
    }
}

/**
 * Retourne le libellé de l'action
 */
function getActionLabel($action) {
    switch ($action) {
        case 'login':
            return 'Connexion';
        case 'logout':
            return 'Déconnexion';
        case 'create':
            return 'Création';
        case 'update':
            return 'Modification';
        case 'delete':
            return 'Suppression';
        case 'download':
            return 'Téléchargement';
        default:
            return ucfirst($action);
    }
}
?>

<?php require_once VIEWS_PATH . 'templates/footer.php'; ?>