<?php
require_once CONTROLLERS_PATH . 'BaseController.php';

/**
 * Contrôleur pour la page d'accueil
 */
class HomeController extends BaseController {
    /**
     * Affiche la page d'accueil
     */
    public function index() {
        // Données à passer à la vue
        $data = [
            'title' => 'Accueil - ' . APP_NAME,
            'description' => 'Système de gestion des archives pour l\'administration publique'
        ];
        
        // Charger la vue
        $this->view('home/index', $data);
    }
    
    /**
     * Affiche la page À propos
     */
    public function about() {
        // Données à passer à la vue
        $data = [
            'title' => 'À propos - ' . APP_NAME,
            'description' => 'Informations sur le système de gestion des archives'
        ];
        
        // Charger la vue
        $this->view('home/about', $data);
    }
    
    /**
     * Affiche la page de contact
     */
    public function contact() {
        // Données à passer à la vue
        $data = [
            'title' => 'Contact - ' . APP_NAME,
            'description' => 'Contactez-nous pour plus d\'informations'
        ];
        
        // Charger la vue
        $this->view('home/contact', $data);
    }
    
    /**
     * Traite le formulaire de contact
     */
    public function sendContact() {
        // Vérifier si c'est une requête POST
        if (!$this->isPostRequest()) {
            $this->redirect('contact');
        }
        
        // Vérifier le jeton CSRF
        if (!$this->validateCsrfToken()) {
            $this->redirect('contact');
        }
        
        // Récupérer et nettoyer les données du formulaire
        $name = sanitize($_POST['name'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $subject = sanitize($_POST['subject'] ?? '');
        $message = sanitize($_POST['message'] ?? '');
        
        // Valider les données
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'Le nom est requis';
        }
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'L\'adresse email est invalide';
        }
        
        if (empty($subject)) {
            $errors[] = 'Le sujet est requis';
        }
        
        if (empty($message)) {
            $errors[] = 'Le message est requis';
        }
        
        // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
        if (!empty($errors)) {
            $_SESSION['contact_errors'] = $errors;
            $_SESSION['contact_form'] = [
                'name' => $name,
                'email' => $email,
                'subject' => $subject,
                'message' => $message
            ];
            $this->redirect('contact');
        }
        
        // Envoyer l'email (à implémenter selon les besoins)
        // ...
        
        // Définir un message de succès
        $this->setFlash('success', 'Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.');
        
        // Rediriger vers la page de contact
        $this->redirect('contact');
    }
}
?>