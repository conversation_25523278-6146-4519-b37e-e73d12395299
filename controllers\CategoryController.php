<?php
require_once CONTROLLERS_PATH . 'BaseController.php';
require_once MODELS_PATH . 'CategoryModel.php';

/**
 * Contrôleur pour la gestion des catégories d'archives
 */
class CategoryController extends BaseController
{
    private $categoryModel;

    /**
     * Constructeur
     */
    public function __construct()
    {
        parent::__construct();
        $this->categoryModel = new CategoryModel();
    }

    /**
     * Affiche la liste des catégories
     */
    public function index()
    {
        // Vérifier si l'utilisateur est connecté
        $this->requireLogin();

        // Paramètres de pagination et de filtrage
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = ITEMS_PER_PAGE;
        $offset = ($page - 1) * $limit;

        // Options de filtrage
        $options = [
            'status' => isset($_GET['status']) ? $_GET['status'] : '',
            'order_by' => isset($_GET['order_by']) ? $_GET['order_by'] : 'name',
            'order_dir' => isset($_GET['order_dir']) ? $_GET['order_dir'] : 'ASC',
            'limit' => $limit,
            'offset' => $offset
        ];

        // Récupérer les catégories
        $categories = $this->categoryModel->getAll($options);
        $totalCategories = $this->categoryModel->count(['status' => $options['status']]);

        // Calculer la pagination
        $totalPages = ceil($totalCategories / $limit);

        // Charger la vue
        $this->view('categories/index', [
            'categories' => $categories,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalCategories' => $totalCategories,
            'options' => $options
        ]);
    }

    /**
     * Affiche le formulaire de création d'une catégorie
     */
    public function create()
    {
        // Vérifier si l'utilisateur est connecté et est administrateur
        $this->requireAdmin();

        // Charger la vue
        $this->view('categories/create');
    }

    /**
     * Traite la soumission du formulaire de création d'une catégorie
     */
    public function store()
    {
        // Vérifier si l'utilisateur est connecté et est administrateur
        $this->requireAdmin();

        // Vérifier si la requête est de type POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            setFlashMessage('error', 'Méthode non autorisée');
            redirect('categories');
            exit;
        }

        // Récupérer et valider les données
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $status = $_POST['status'] ?? 'active';

        $errors = [];

        // Validation du nom
        if (empty($name)) {
            $errors['name'] = 'Le nom de la catégorie est requis';
        } elseif (strlen($name) > 100) {
            $errors['name'] = 'Le nom de la catégorie ne doit pas dépasser 100 caractères';
        } elseif ($this->categoryModel->nameExists($name)) {
            $errors['name'] = 'Ce nom de catégorie existe déjà';
        }

        // Validation de la description
        if (strlen($description) > 255) {
            $errors['description'] = 'La description ne doit pas dépasser 255 caractères';
        }

        // Validation du statut
        if (!in_array($status, ['active', 'inactive'])) {
            $errors['status'] = 'Le statut doit être "active" ou "inactive"';
        }

        // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
        if (!empty($errors)) {
            $_SESSION['form_data'] = $_POST;
            $_SESSION['form_errors'] = $errors;
            redirect('categories/create');
            exit;
        }

        // Créer la catégorie
        $categoryData = [
            'name' => $name,
            'description' => $description,
            'status' => $status
        ];

        $categoryId = $this->categoryModel->create($categoryData);

        if ($categoryId) {
            // Succès
            setFlashMessage('success', 'La catégorie a été créée avec succès');
            redirect('categories');
        } else {
            // Erreur
            setFlashMessage('error', 'Une erreur est survenue lors de la création de la catégorie');
            $_SESSION['form_data'] = $_POST;
            redirect('categories/create');
        }
    }

    /**
     * Affiche le formulaire de modification d'une catégorie
     */
    public function edit()
    {
        // Vérifier si l'utilisateur est connecté et est administrateur
        $this->requireAdmin();

        // Récupérer l'ID de la catégorie
        $id = $_GET['id'] ?? 0;

        // Récupérer la catégorie
        $category = $this->categoryModel->findById($id);

        if (!$category) {
            setFlashMessage('error', 'Catégorie non trouvée');
            redirect('categories');
            exit;
        }

        // Charger la vue
        $this->view('categories/edit', ['category' => $category]);
    }

    /**
     * Traite la soumission du formulaire de modification d'une catégorie
     */
    public function update()
    {
        // Vérifier si l'utilisateur est connecté et est administrateur
        $this->requireAdmin();

        // Récupérer l'ID de la catégorie
        $id = $_POST['id'] ?? $_GET['id'] ?? 0;

        // Vérifier si la requête est de type POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            setFlashMessage('error', 'Méthode non autorisée');
            redirect('categories');
            exit;
        }

        // Récupérer la catégorie
        $category = $this->categoryModel->findById($id);

        if (!$category) {
            setFlashMessage('error', 'Catégorie non trouvée');
            redirect('categories');
            exit;
        }

        // Récupérer et valider les données
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $status = $_POST['status'] ?? 'active';

        $errors = [];

        // Validation du nom
        if (empty($name)) {
            $errors['name'] = 'Le nom de la catégorie est requis';
        } elseif (strlen($name) > 100) {
            $errors['name'] = 'Le nom de la catégorie ne doit pas dépasser 100 caractères';
        } elseif ($this->categoryModel->nameExists($name, $id)) {
            $errors['name'] = 'Ce nom de catégorie existe déjà';
        }

        // Validation de la description
        if (strlen($description) > 255) {
            $errors['description'] = 'La description ne doit pas dépasser 255 caractères';
        }

        // Validation du statut
        if (!in_array($status, ['active', 'inactive'])) {
            $errors['status'] = 'Le statut doit être "active" ou "inactive"';
        }

        // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
        if (!empty($errors)) {
            $_SESSION['form_data'] = $_POST;
            $_SESSION['form_errors'] = $errors;
            redirect('categories/edit?id=' . $id);
            exit;
        }

        // Mettre à jour la catégorie
        $categoryData = [
            'name' => $name,
            'description' => $description,
            'status' => $status
        ];

        $success = $this->categoryModel->update($id, $categoryData);

        if ($success) {
            // Succès
            setFlashMessage('success', 'La catégorie a été mise à jour avec succès');
            redirect('categories');
        } else {
            // Erreur
            setFlashMessage('error', 'Une erreur est survenue lors de la mise à jour de la catégorie');
            $_SESSION['form_data'] = $_POST;
            redirect('categories/edit?id=' . $id);
        }
    }

    /**
     * Supprime une catégorie
     */
    public function delete()
    {
        // Vérifier si l'utilisateur est connecté et est administrateur
        $this->requireAdmin();

        // Récupérer l'ID de la catégorie
        $id = $_POST['id'] ?? $_GET['id'] ?? 0;

        // Vérifier si la requête est de type POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            setFlashMessage('error', 'Méthode non autorisée');
            redirect('categories');
            exit;
        }

        // Récupérer la catégorie
        $category = $this->categoryModel->findById($id);

        if (!$category) {
            setFlashMessage('error', 'Catégorie non trouvée');
            redirect('categories');
            exit;
        }

        // Supprimer la catégorie
        $success = $this->categoryModel->delete($id);

        if ($success) {
            // Succès
            setFlashMessage('success', 'La catégorie a été supprimée avec succès');
        } else {
            // Erreur
            setFlashMessage('error', 'Impossible de supprimer cette catégorie car elle est utilisée par des archives');
        }

        redirect('categories');
    }
}