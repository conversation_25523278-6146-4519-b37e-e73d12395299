<?php
/**
 * Helper pour la génération d'URLs
 */

/**
 * Génère une URL en fonction de la configuration
 * @param string $path Le chemin de l'URL
 * @return string L'URL complète
 */
function url($path = '') {
    // Enlever le slash initial s'il existe
    $path = ltrim($path, '/');
    
    // Si le chemin est vide, retourner l'URL de base
    if (empty($path)) {
        return BASE_URL;
    }
    
    // Vérifier si mod_rewrite fonctionne (simple test)
    static $rewriteWorks = null;
    
    if ($rewriteWorks === null) {
        // Test simple : si on arrive ici via une URL propre, mod_rewrite fonctionne
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        
        // Si REQUEST_URI ne contient pas index.php mais qu'on est dans index.php
        if (strpos($requestUri, 'index.php') === false && 
            strpos($scriptName, 'index.php') !== false &&
            isset($_GET['url'])) {
            $rewriteWorks = true;
        } else {
            $rewriteWorks = false;
        }
    }
    
    if ($rewriteWorks) {
        // Utiliser les URLs propres
        return BASE_URL . $path;
    } else {
        // Utiliser les paramètres GET
        return BASE_URL . 'index.php?url=' . urlencode($path);
    }
}

/**
 * Génère une URL pour une action spécifique
 * @param string $controller Le contrôleur
 * @param string $action L'action
 * @param array $params Les paramètres supplémentaires
 * @return string L'URL complète
 */
function action_url($controller, $action = 'index', $params = []) {
    $path = strtolower($controller);
    
    if ($action !== 'index') {
        $path .= '/' . $action;
    }
    
    $url = url($path);
    
    if (!empty($params)) {
        $separator = (strpos($url, '?') !== false) ? '&' : '?';
        $url .= $separator . http_build_query($params);
    }
    
    return $url;
}

/**
 * Redirige vers une URL
 * @param string $path Le chemin de redirection
 */
function redirect($path = '') {
    $url = url($path);
    header('Location: ' . $url);
    exit;
}

/**
 * Vérifie si mod_rewrite est disponible
 * @return bool
 */
function is_rewrite_enabled() {
    if (function_exists('apache_get_modules')) {
        return in_array('mod_rewrite', apache_get_modules());
    }
    
    // Test alternatif : vérifier si .htaccess existe et si on peut l'utiliser
    return file_exists('.htaccess');
}
?>
