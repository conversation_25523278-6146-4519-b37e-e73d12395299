<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erreur - <?= APP_NAME ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-body text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                        </div>
                        
                        <h1 class="display-4 text-danger mb-3">
                            <?php
                            $code = http_response_code();
                            switch($code) {
                                case 404:
                                    echo "404";
                                    break;
                                case 403:
                                    echo "403";
                                    break;
                                case 500:
                                    echo "500";
                                    break;
                                default:
                                    echo "Erreur";
                            }
                            ?>
                        </h1>
                        
                        <h2 class="h4 mb-3">
                            <?php
                            switch($code) {
                                case 404:
                                    echo "Page non trouvée";
                                    break;
                                case 403:
                                    echo "Accès interdit";
                                    break;
                                case 500:
                                    echo "Erreur interne du serveur";
                                    break;
                                default:
                                    echo "Une erreur s'est produite";
                            }
                            ?>
                        </h2>
                        
                        <p class="text-muted mb-4">
                            <?php
                            if (isset($message)) {
                                echo htmlspecialchars($message);
                            } else {
                                switch($code) {
                                    case 404:
                                        echo "La page que vous recherchez n'existe pas ou a été déplacée.";
                                        break;
                                    case 403:
                                        echo "Vous n'avez pas les permissions nécessaires pour accéder à cette ressource.";
                                        break;
                                    case 500:
                                        echo "Une erreur interne s'est produite. Veuillez réessayer plus tard.";
                                        break;
                                    default:
                                        echo "Une erreur inattendue s'est produite.";
                                }
                            }
                            ?>
                        </p>
                        
                        <div class="d-flex justify-content-center gap-3">
                            <a href="<?= BASE_URL ?>" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>Retour à l'accueil
                            </a>
                            <button onclick="history.back()" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Page précédente
                            </button>
                        </div>
                    </div>
                </div>
                
                <?php if (defined('DEBUG') && DEBUG): ?>
                <div class="card mt-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">Informations de débogage</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Code d'erreur:</strong> <?= $code ?></p>
                        <?php if (isset($message)): ?>
                        <p><strong>Message:</strong> <?= htmlspecialchars($message) ?></p>
                        <?php endif; ?>
                        <p><strong>URL demandée:</strong> <?= htmlspecialchars($_SERVER['REQUEST_URI'] ?? 'N/A') ?></p>
                        <p><strong>Méthode:</strong> <?= htmlspecialchars($_SERVER['REQUEST_METHOD'] ?? 'N/A') ?></p>
                        <p><strong>User Agent:</strong> <?= htmlspecialchars($_SERVER['HTTP_USER_AGENT'] ?? 'N/A') ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
