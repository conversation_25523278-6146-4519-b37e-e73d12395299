<?php
require_once __DIR__ . '/../config/database.php';

/**
 * Modèle pour la gestion des catégories d'archives
 */
class CategoryModel
{
    private $db;
    private $table = 'categories';

    /**
     * Constructeur
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Récupère toutes les catégories
     * @param array $options Options de filtrage et de pagination
     * @return array Les catégories
     */
    public function getAll($options = [])
    {
        // Construire la requête SQL
        $sql = "SELECT * FROM {$this->table}";

        // Filtrage par statut
        if (isset($options['status']) && !empty($options['status'])) {
            $sql .= " WHERE status = :status";
        }

        // Tri
        $sql .= " ORDER BY " . ($options['order_by'] ?? 'name') . " " . ($options['order_dir'] ?? 'ASC');

        // Pagination
        if (isset($options['limit']) && isset($options['offset'])) {
            $sql .= " LIMIT :offset, :limit";
        }

        // Préparer les paramètres
        $params = [];

        if (isset($options['status']) && !empty($options['status'])) {
            $params['status'] = $options['status'];
        }

        if (isset($options['limit']) && isset($options['offset'])) {
            $params['limit'] = $options['limit'];
            $params['offset'] = $options['offset'];
        }

        // Exécuter la requête
        return $this->db->all($sql, $params);
    }

    /**
     * Compte le nombre total de catégories
     * @param array $options Options de filtrage
     * @return int Le nombre de catégories
     */
    public function count($options = [])
    {
        // Construire la requête SQL
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";

        // Filtrage par statut
        if (isset($options['status']) && !empty($options['status'])) {
            $sql .= " WHERE status = :status";
        }

        // Préparer les paramètres
        $params = [];

        if (isset($options['status']) && !empty($options['status'])) {
            $params['status'] = $options['status'];
        }

        // Exécuter la requête
        $result = $this->db->single($sql, $params);

        return $result['count'];
    }

    /**
     * Récupère une catégorie par son ID
     * @param int $id L'ID de la catégorie
     * @return array|false La catégorie ou false si non trouvée
     */
    public function findById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = :id";
        return $this->db->single($sql, ['id' => $id]);
    }

    /**
     * Récupère une catégorie par son nom
     * @param string $name Le nom de la catégorie
     * @return array|false La catégorie ou false si non trouvée
     */
    public function findByName($name)
    {
        $sql = "SELECT * FROM {$this->table} WHERE name = :name";
        return $this->db->single($sql, ['name' => $name]);
    }

    /**
     * Vérifie si un nom de catégorie existe déjà
     * @param string $name Le nom à vérifier
     * @param int $excludeId ID de la catégorie à exclure (pour les mises à jour)
     * @return bool True si le nom existe, false sinon
     */
    public function nameExists($name, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE name = :name";
        $params = ['name' => $name];

        if ($excludeId !== null) {
            $sql .= " AND id != :id";
            $params['id'] = $excludeId;
        }

        $result = $this->db->single($sql, $params);

        return $result['count'] > 0;
    }

    /**
     * Crée une nouvelle catégorie
     * @param array $data Les données de la catégorie
     * @return int|false L'ID de la catégorie créée ou false en cas d'erreur
     */
    public function create($data)
    {
        $sql = "INSERT INTO {$this->table} (name, description, status, created_at) 
                VALUES (:name, :description, :status, NOW())";

        $params = [
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'status' => $data['status'] ?? 'active'
        ];

        $this->db->query($sql, $params);

        return $this->db->lastInsertId();
    }

    /**
     * Met à jour une catégorie
     * @param int $id L'ID de la catégorie
     * @param array $data Les données à mettre à jour
     * @return bool True si la mise à jour a réussi, false sinon
     */
    public function update($id, $data)
    {
        // Construire la requête SQL
        $sql = "UPDATE {$this->table} SET ";
        $updates = [];
        $params = ['id' => $id];

        // Ajouter les champs à mettre à jour
        if (isset($data['name'])) {
            $updates[] = "name = :name";
            $params['name'] = $data['name'];
        }

        if (isset($data['description'])) {
            $updates[] = "description = :description";
            $params['description'] = $data['description'];
        }

        if (isset($data['status'])) {
            $updates[] = "status = :status";
            $params['status'] = $data['status'];
        }

        $updates[] = "updated_at = NOW()";

        $sql .= implode(", ", $updates);
        $sql .= " WHERE id = :id";

        // Exécuter la requête
        $this->db->query($sql, $params);

        return true;
    }

    /**
     * Supprime une catégorie
     * @param int $id L'ID de la catégorie
     * @return bool True si la suppression a réussi, false sinon
     */
    public function delete($id)
    {
        // Vérifier si la catégorie est utilisée par des archives
        $checkSql = "SELECT COUNT(*) as count FROM archives WHERE category_id = :id";
        $result = $this->db->single($checkSql, ['id' => $id]);

        if ($result['count'] > 0) {
            // La catégorie est utilisée, ne pas supprimer
            return false;
        }

        // Supprimer la catégorie
        $sql = "DELETE FROM {$this->table} WHERE id = :id";
        $this->db->query($sql, ['id' => $id]);

        return true;
    }

    /**
     * Récupère les catégories pour un menu déroulant
     * @param bool $includeInactive Inclure les catégories inactives
     * @return array Les catégories pour le menu déroulant
     */
    public function getForDropdown($includeInactive = false)
    {
        $sql = "SELECT id, name FROM {$this->table}";

        if (!$includeInactive) {
            $sql .= " WHERE status = 'active'";
        }

        $sql .= " ORDER BY name ASC";

        return $this->db->all($sql);
    }
}