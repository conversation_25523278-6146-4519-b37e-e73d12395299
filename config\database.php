<?php
// Configuration de la base de données

// Paramètres de connexion à la base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'archive_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

/**
 * Classe de connexion à la base de données
 */
class Database {
    private static $instance = null;
    private $conn;
    
    /**
     * Constructeur privé pour empêcher l'instanciation directe
     */
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->conn = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            die("Erreur de connexion à la base de données: " . $e->getMessage());
        }
    }
    
    /**
     * Méthode pour obtenir l'instance unique de la classe (Singleton)
     * @return Database L'instance unique de la classe
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Méthode pour obtenir la connexion PDO
     * @return PDO La connexion PDO
     */
    public function getConnection() {
        return $this->conn;
    }
    
    /**
     * Méthode pour exécuter une requête SQL
     * @param string $sql La requête SQL à exécuter
     * @param array $params Les paramètres de la requête
     * @return PDOStatement Le résultat de la requête
     */
    public function query($sql, $params = []) {
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }
    
    /**
     * Méthode pour obtenir un seul enregistrement
     * @param string $sql La requête SQL à exécuter
     * @param array $params Les paramètres de la requête
     * @return array|false L'enregistrement ou false si aucun enregistrement n'est trouvé
     */
    public function single($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Méthode pour obtenir tous les enregistrements
     * @param string $sql La requête SQL à exécuter
     * @param array $params Les paramètres de la requête
     * @return array Les enregistrements
     */
    public function all($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Méthode pour obtenir le dernier ID inséré
     * @return string Le dernier ID inséré
     */
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }
    
    /**
     * Méthode pour commencer une transaction
     */
    public function beginTransaction() {
        $this->conn->beginTransaction();
    }
    
    /**
     * Méthode pour valider une transaction
     */
    public function commit() {
        $this->conn->commit();
    }
    
    /**
     * Méthode pour annuler une transaction
     */
    public function rollback() {
        $this->conn->rollBack();
    }
}
?>